import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";

const PopulationChart = ({
  categories,
  data,
  type = "column",
  YgridLineWidth = 0,
  XgridLineWidth = 0,
  tooltipFormatter,
}) => {
  const options = {
    chart: {
      type,
    },
    title: {
      text: null,
    },
    subtitle: {
      text: null,
    },
    xAxis: {
      categories,
      title: { text: null },
      gridLineWidth: XgridLineWidth,
      lineWidth: 0,
    },
    yAxis: {
      min: 0,
      max: 100,
      title: {
        text: null,
        align: "high",
      },
      labels: {
        formatter: function () {
          return toPersianNumber(this.value.toFixed(0)) + "%";
        },
        overflow: "justify",
      },
      gridLineWidth: YgridLineWidth,
      gridLineDashStyle: "dash",
    },
    tooltip: {
      headerFormat: "",
      backgroundColor: "#efefef",
      shadow: false,
      useHTML: true,
      formatter: tooltipFormatter,
    },
    plotOptions: {
      column: {
        borderRadius: "50%",
        dataLabels: { enabled: false },
        groupPadding: 0.2, // Adjusted for better spacing between groups
        pointPadding: 0.1,
        pointWidth: 20,
      },
    },
    credits: { enabled: false },
    series: data,
  };

  return (
    <>
      {data && data.length > 0 ? (
        <HighchartsReact highcharts={Highcharts} options={options} />
      ) : (
        <div className="w-full h-[400px] flex justify-center pt-4 items-center text-lg text-gray-600 font-body-bold-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      )}
    </>
  );
};

export default PopulationChart;
