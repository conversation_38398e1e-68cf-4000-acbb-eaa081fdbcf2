import { useEffect, useMemo, useState } from "react";
import { useLayoutContext } from "../context/layout-context";
import useSearchStore from "../store/searchStore.js";

const CardsLayout = ({ children }) => {
  const { isFilterListOpen, showFilterList } = useSearchStore();
  const { isSidebarOpened } = useLayoutContext();
  const [widthOfScreen, setWidthOfScreen] = useState(window.innerWidth);

  const countsOfCol = () => {
    let colCount = 4;
    if (widthOfScreen < 1440) colCount--;
    if (showFilterList && isFilterListOpen) colCount--;
    if (isSidebarOpened) colCount--;
    return colCount;
  };

  const cols = useMemo(() => {
    return countsOfCol();
  }, [isFilterListOpen, isSidebarOpened, widthOfScreen]);

  useEffect(() => {
    window.addEventListener("resize", () => {
      setWidthOfScreen(window.innerWidth);
    });
  }, []);

  return (
    <div
      className="grid gap-4 [direction:rtl]"
      style={{ gridTemplateColumns: `repeat(${cols},1fr)` }}
    >
      {children}
    </div>
  );
};

export default CardsLayout;
