import PropTypes from "prop-types";
import { Card } from "components/ui/Card";
import "../../style.css";
import { useReport360Store } from "store/report360Store";
import MediaBadge from "components/ui/MediaBadge";
import DateFilter from "../../step-2-profile/components/SearchBar/DateFilter";
import { CButton } from "components/ui/CButton";
import use360requestStore from "store/360requestStore";
import report360 from "service/api/report360";
import { CInput } from "components/ui/CInput";
import Popup from "components/ui/PopUp";
import { memo, useEffect, useState, useRef, useCallback } from "react";
import { notification, toPersianNumber } from "utils/helper";
import {
  CheckCircle,
  User,
  WarningDiamond,
  Image,
  Link,
} from "@phosphor-icons/react";
import FetchImage from "pages/user/show-profile/components/FetchImage";
import Loading from "components/ui/Loading";
import fallbackImg from "assets/images/default.png";
import { useNavigate } from "react-router-dom";

const UserInfo = ({ isFixed, activeSpan, isEdit }) => {
  const { type, date, profile, id, profiles, currentProfileId } =
    useReport360Store((state) => state.report);
  const [isOpen, setIsOpen] = useState(false);

  const [savedProfiles, setSavedProfiles] = useState(new Set());

  const initializedRef = useRef(false);

  const currentProfile = currentProfileId
    ? profiles[currentProfileId] || profile
    : profile;

  const isCurrentProfileSaved = useCallback(() => {
    if (isEdit && id) {
      if (!currentProfileId) {
        return true;
      } else {
        const currentId =
          currentProfile.id ||
          currentProfile.user_name ||
          currentProfile.key ||
          "default";
        return savedProfiles.has(currentId);
      }
    }

    const currentId =
      currentProfile.id ||
      currentProfile.user_name ||
      currentProfile.key ||
      "default";
    return savedProfiles.has(currentId);
  }, [isEdit, id, currentProfile, currentProfileId, savedProfiles]);

  const getButtonText = () => {
    return isCurrentProfileSaved() ? "بروزرسانی استعلام" : "ذخیره استعلام";
  };

  const [title, setTitle] = useState(
    currentProfile.user_title ||
      currentProfile.channel_title ||
      currentProfile.full_name ||
      ""
  );
  const setReport = useReport360Store((state) => state.setReport);
  const updateReport = use360requestStore((state) => state.updateReportField);
  const platform = currentProfile.platform;
  const [loadingAvatar, setLoadingAvatar] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(null);
  const [backgroundImage, setBackgroundImage] = useState(null);
  const navigate = useNavigate();
  const fileInputRef = useRef(null);

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setReport({ date: { from, to } });
  };

  const handleTitle = (e) => {
    setTitle(e.target.value);
    updateReport("title", e.target.value);
  };

  const handleAvatarClick = () => fileInputRef.current.click();

  const handleFileChange = async (event) => {
    setLoadingAvatar(true);
    const file = event.target.files[0];

    if (!file) {
      setLoadingAvatar(false);
      return;
    }

    const maxSizeInBytes = 1 * 1024 * 1024; // 1 MB

    if (file.size > maxSizeInBytes) {
      notification.error(
        "حجم فایل انتخاب شده نباید بیشتر از 1 مگابایت باشد.",
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
      setLoadingAvatar(false);
      return;
    }

    if (file.type === "image/png" || file.type === "image/jpeg") {
      try {
        const formData = new FormData();
        formData.append("image", file);

        const uploadedImageRes = await report360.image(formData);
        const avatarUrl = uploadedImageRes.data.data.image;

        const imageUrl = URL.createObjectURL(file);
        setBackgroundImage(imageUrl);

        setAvatarUrl(avatarUrl);
      } catch (error) {
        notification.error(
          "آپلود تصویر با خطا مواجه شد.",
          <WarningDiamond size={32} className="text-light-error-text-rest" />
        );
      }
    } else {
      notification.error(
        "فرمت فایل انتخاب شده صحیح نیست.",
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
    }

    setLoadingAvatar(false);
  };

  useEffect(() => {
    if (title === "") {
      updateReport(
        "title",
        currentProfile.user_title ||
          currentProfile?.channel_title ||
          currentProfile.full_name
      );
    } else {
      updateReport("title", title);
    }
  }, [
    currentProfile.user_title,
    currentProfile.channel_title,
    currentProfile.full_name,
    title,
    updateReport,
  ]);

  const handleReportSave = async () => {
    try {
      let response;
      updateReport("content.report_platform", platform);
      updateReport("description", currentProfile?.bio || "");
      updateReport(
        "content.source_info.avatar",
        avatarUrl ?? currentProfile?.avatar ?? currentProfile.logo_image
      );
      updateReport(
        "q",
        currentProfile?.user_title ||
          currentProfile?.channel_title ||
          currentProfile.full_name
      );
      updateReport(
        "title",
        title ||
          currentProfile?.user_title ||
          currentProfile?.channel_title ||
          currentProfile.full_name
      );
      updateReport(
        "content.source_info.source_name",
        currentProfile?.user_name ?? currentProfile?.channel_username
      );
      updateReport(
        "content.source_info.source_id",
        currentProfile?.id ?? currentProfile?.channel_id
      );

      const reportState = use360requestStore.getState().report;
      const profileId =
        currentProfile.id ||
        currentProfile.user_name ||
        currentProfile.key ||
        "default";

      if (isCurrentProfileSaved()) {
        if (!id) {
          notification.error("شناسه گزارش معتبر نیست.");
          return;
        }
        response = await report360.update(id, reportState);
        if (response?.data?.status === "OK") {
          notification.success(
            `گزارش ${toPersianNumber(360)} با موفقیت بروزرسانی شد`,
            <CheckCircle className="text-light-success-text-rest" />
          );
        }
      } else {
        response = await report360.create(reportState);
        if (response?.data?.status === "OK") {
          notification.success(
            `گزارش ${toPersianNumber(360)} با موفقیت ذخیره شد`,
            <CheckCircle className="text-light-success-text-rest" />
          );
          const newId = response?.data?.data?.id;
          setReport({ id: newId }); // Update store with new id

          // Mark this profile as saved
          setSavedProfiles((prev) => new Set([...prev, profileId]));

          navigate(`/app/report-360/edit`, { state: { id: newId } });
        }
      }
      setIsOpen(false);
    } catch (error) {
      console.error("Error saving report:", error);
      notification.error(
        `با عرض پوزش، امکان ذخیره 'گزارش' وجود ندارد. لطفا پس از چند لحظه دوباره تلاش کنید و در صورت عدم حل مشکل با پشتیبانی تماس بگیرید.`
      );
    }
  };

  useEffect(() => {
    const newTitle =
      currentProfile.user_title ||
      currentProfile.channel_title ||
      currentProfile.full_name ||
      "";
    setTitle(newTitle);

    const newBackgroundImage =
      currentProfile?.avatar || currentProfile?.logo_image || null;

    setBackgroundImage(newBackgroundImage);
  }, [
    currentProfile.user_title,
    currentProfile.channel_title,
    currentProfile.full_name,
    currentProfile.avatar,
    currentProfile.logo_image,
    currentProfileId,
  ]);

  useEffect(() => {
    if (!backgroundImage && currentProfile) {
      const initialBackgroundImage =
        currentProfile?.avatar || currentProfile?.logo_image || null;
      if (initialBackgroundImage) {
        setBackgroundImage(initialBackgroundImage);
      }
    }
  }, [currentProfile, backgroundImage]);

  useEffect(() => {
    const { from, to } = date;
    updateReport("start_date", from);
    updateReport("end_date", to);
  }, [date, currentProfile, updateReport]);

  useEffect(() => {
    if (isEdit && id && profile && !initializedRef.current) {
      const originalProfileId =
        profile.id || profile.user_name || profile.key || "default";
      setSavedProfiles((prev) => new Set([...prev, originalProfileId]));
      initializedRef.current = true;
    }
  }, [isEdit, id, profile]);

  const handleKeyDown = async (e) => {
    if (e.key === "Enter") {
      await handleReportSave();
    }
  };

  const getPlatformUrl = (platform, username) => {
    if (!username) return "#";
    const baseUrls = {
      telegram: `https://t.me/${username}`,
      instagram: `https://www.instagram.com/${username}`,
      twitter: `https://twitter.com/${username}`,
      news: username,
    };
    return baseUrls[platform] || "#";
  };

  return (
    <>
      <Card
        className={`flex justify-between items-center !px-6 gap-5 border-r-8 border-[#4D36BF] shadow-xl ${
          isFixed && "mt-11 shadow-xl info-box"
        } card-animation card-delay mb-4`}
      >
        <div className="flex flex-col gap-5">
          <div className="flex items-center gap-3 ">
            <img
              src={currentProfile.avatar || currentProfile.logo_image}
              alt="user avatar"
              className="relative w-[5rem] h-[5rem] rounded-full z-[10] mask-image-avatar"
              onError={(e) => {
                e.target.onError = null;
                e.target.src = fallbackImg;
              }}
            />
            <span
              className={`absolute ${
                !isFixed ? "top-[59%]" : "top-[72%]"
              } left-[97%] transform -translate-x-1/2 -translate-y-1/2 rounded-full !w-[40px] !h-[40px] z-[10]`}
            >
              <MediaBadge
                media={platform}
                className="!h-[30px] !w-[30px] !rounded-[50px]"
                size="normal"
              />
            </span>
            <div className="flex flex-col gap-1">
              <div className="flex gap-2 items-center">
                <h3 className="font-headline-small">
                  {currentProfile.full_name ||
                    currentProfile.title ||
                    currentProfile.user_title ||
                    currentProfile.channel_title}
                </h3>
                <a
                  className="font-headline-small pb-1 hover:text-blue-500"
                  href={getPlatformUrl(
                    platform,
                    currentProfile?.user_name ||
                      currentProfile?.channel_username ||
                      currentProfile?.base_url
                  )}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Link />
                </a>
              </div>
              {platform === "news" ? (
                <p className="font-body-small text-[#8f8f8f]">
                  {currentProfile.base_url}
                </p>
              ) : (
                <p className="font-body-small text-[#8f8f8f]">
                  {currentProfile.user_name ||
                    currentProfile.source_name ||
                    currentProfile.channel_username}
                  @
                </p>
              )}
            </div>
          </div>
          {!isFixed && (
            <p className="font-body-small w-[80%]">
              {currentProfile.bio || currentProfile.description}
            </p>
          )}
        </div>
        <div>
          <div className="flex pt-1 gap-10 z-[10]">
            <DateFilter
              type={type}
              handleDateChange={handleDateChange}
              selectedDateRange={date}
            />
            <CButton
              type={"submit"}
              onClick={() =>
                !isCurrentProfileSaved() ? setIsOpen(true) : handleReportSave()
              }
              size={"lg"}
              className={"[direction:rtl] [width:170px!important]"}
            >
              {getButtonText()}
            </CButton>
          </div>
        </div>
      </Card>
      <Popup
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title={"ذخیره 'گزارش 360 '"}
        submitHandler={handleReportSave}
      >
        <div className="flex flex-col gap-6">
          <div className={"flex justify-center mb-4"}>
            <FetchImage
              imageUrl={backgroundImage}
              onFetchSuccess={setBackgroundImage}
            />
            <div
              className={
                "flex relative cursor-pointer w-[120px] h-[120px] bg-light-neutral-background-medium" +
                " items-center align-middle text-center group bg-center bg-no-repeat bg-contain"
              }
              style={{
                clipPath: "circle(50%)",
                backgroundImage: backgroundImage
                  ? `url(${backgroundImage})`
                  : "none",
              }}
              onClick={handleAvatarClick}
            >
              {!loadingAvatar ? (
                !backgroundImage && (
                  <User
                    className={"w-full text-light-primary-text-rest"}
                    size={40}
                  />
                )
              ) : (
                <Loading className={"relative w-full h-full"} />
              )}
              <div
                className={
                  "flex w-full absolute bottom-0 bg-light-primary-background-highlight text-light-neutral-text-low h-[22px] text-center opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100"
                }
              >
                <Image size={20} style={{ margin: "0 auto" }} />
              </div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                max={"1m"}
                onChange={handleFileChange}
              />
            </div>
          </div>
          <div className="font-overline-large">
            این گزارش را با نتایج موجود ذخیره کنید تا در آینده بتوانید به آن
            رجوع کنید.
          </div>
          <div>
            <CInput
              title="عنوان گزارش"
              type="text"
              onChange={handleTitle}
              value={title}
              placeholder={`برای گزارش ${toPersianNumber(
                360
              )} منبع یک عنوان بنویسید`}
              inputProps={{ onKeyDown: handleKeyDown }}
            />
          </div>
        </div>
      </Popup>
    </>
  );
};

UserInfo.propTypes = {
  isFixed: PropTypes.bool.isRequired,
  isEdit: PropTypes.bool,
  activeSpan: PropTypes.string,
};

export default memo(UserInfo);
