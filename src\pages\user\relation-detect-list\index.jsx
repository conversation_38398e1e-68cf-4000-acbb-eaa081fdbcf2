import Loading from "components/ui/Loading";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import Wrapper from "./components/Wrapper";
import { ToastContainer } from "react-toastify";
import { useEffect, useState } from "react";
import RelationDetectTable from "./components/RelationDetectTable";
import Empty from "./components/Empty";
import relationDetect from "service/api/relationDetect";

const RelationDetectList = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [relationDetectList, setRelationDetectList] = useState([]);
  const breadcrumbList = [{ title: "شناسایی روابط" }];
  useBreadcrumb(breadcrumbList);

  const getList = async () => {
    setIsLoading(true);
    try {
      const {
        data: { data },
      } = await relationDetect?.getGraphsList();
      setRelationDetectList(data);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setIsLoading(false);
  };
  useEffect(() => {
    getList();
  }, []);

  return (
    <div>
      <>
        {isLoading ? (
          <Loading />
        ) : (
          <>
            <div className="flex flex-col h-full w-full px-6">
              <Wrapper>
                {relationDetectList.length > 0 ? (
                  relationDetectList.map((item) => (
                    <RelationDetectTable key={item.id} data={item} />
                  ))
                ) : (
                  <Empty />
                )}
              </Wrapper>
            </div>
          </>
        )}
        <ToastContainer />
      </>
    </div>
  );
};

export default RelationDetectList;
