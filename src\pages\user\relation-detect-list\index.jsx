import Loading from "components/ui/Loading";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import Wrapper from "./components/Wrapper";
import { ToastContainer } from "react-toastify";
import { useEffect, useState } from "react";
import RelationDetectTable from "./components/RelationDetectTable";
import Empty from "./components/Empty";
import relationDetect from "service/api/relationDetect";
import { Link } from "react-router-dom";
import { CButton } from "components/ui/CButton";
import { Plus, Question } from "@phosphor-icons/react";

const RelationDetectList = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [relationDetectList, setRelationDetectList] = useState([]);
  const breadcrumbList = [{ title: "شناسایی روابط" }];
  useBreadcrumb(breadcrumbList);

  const getList = async () => {
    setIsLoading(true);
    try {
      const {
        data: { data },
      } = await relationDetect?.getGraphsList();
      setRelationDetectList(data);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setIsLoading(false);
  };
  useEffect(() => {
    getList();
  }, []);

  return (
    <div>
      <>
        {isLoading ? (
          <Loading />
        ) : (
          <>
            <div className="flex flex-col h-full w-full px-6">
              <div className="flex justify-end gap-2 pb-4">
                <Link to={"/app/relation-detect/create"} className="">
                  <CButton
                    rightIcon={<Plus />}
                    width={180}
                    className={"[direction:ltr]"}
                  >
                    گراف روابط جدید
                  </CButton>
                </Link>

                <CButton
                  rightIcon={
                    <Question
                      className="text-light-neutral-text-medium"
                      size={18}
                    />
                  }
                  width={200}
                  className={"[direction:ltr] gap-2 !h-10"}
                  mode="outline"
                  role="neutral"
                >
                  <p className="text-light-neutral-text-medium font-button-medium">
                    شناسایی روابط چیست
                  </p>
                </CButton>
              </div>

              <Wrapper>
                {relationDetectList.length > 0 ? (
                  relationDetectList.map((item) => (
                    <RelationDetectTable key={item.id} data={item} />
                  ))
                ) : (
                  <Empty />
                )}
              </Wrapper>
            </div>
          </>
        )}
        <ToastContainer />
      </>
    </div>
  );
};

export default RelationDetectList;
