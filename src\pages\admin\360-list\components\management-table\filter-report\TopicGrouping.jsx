import ColumnChart from "./charts/ColumnChart";
import HorizontalBar from "components/Charts/HorizontalBar.jsx";
import { Card } from "components/ui/Card.jsx";

const TopicGrouping = () => {
  return (
    <Card className="flex flex-col gap-2 h-full">
      <div className="flex items-center gap-1">
        <p className="font-subtitle-large">
          کاربرانی که بیشترین فیلتر را ثبت کرده اند
        </p>
      </div>

      <HorizontalBar
        colors={[
          "#292FB8",
          "#4047E5",
          "#5C63FF",
          "#6D72E5",
          "#8A8EF1",
          "#D7D9FF",
        ]}
        info={[
          { count: 1000 },
          { count: 600 },
          { count: 500 },
          { count: 400 },
          { count: 200 },
          { count: 100 },
        ]}
        loading={false}
        categories={[
          "نام کاربر سیناپس شماره 1",
          "نام کاربر سیناپس شماره 2",
          "نام کاربر سیناپس شماره 3",
          "نام کاربر سیناپس شماره 4",
          "نام کاربر سیناپس شماره 5",
          "نام کاربر سیناپس شماره 6",
        ]}
        minWidth={"15rem"}
        maxWidth="100%"
      />

      {/*<ColumnChart*/}
      {/*  xAxisCategory={[*/}
      {/*    "فرهنگ",*/}
      {/*    "اجتماعی",*/}
      {/*    "ورزشی",*/}
      {/*    "سیاسی",*/}
      {/*    "اقتصادی",*/}
      {/*    "علمی",*/}
      {/*    "هنر و رسانه",*/}
      {/*    "روزمره",*/}
      {/*    "بین الملل",*/}
      {/*  ]}*/}
      {/*  seriesColor={"#1DCEA3"}*/}
      {/*/>*/}
    </Card>
  );
};

export default TopicGrouping;
