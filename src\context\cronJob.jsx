import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import bulletin from "service/api/bulletin.js";
import { useBulletinStore } from "store/bulletinStore.js";
import { CheckCircle } from "@phosphor-icons/react";
import { notification as notif } from "utils/helper.js";
import notification from "service/api/notification.js";
import AuthContext from "context/auth-context.jsx";
const CronJobContext = createContext();

const TRY_LIMIT = 49;

export const useCronJob = () => {
  return useContext(CronJobContext);
};

export const CronJobProvider = ({ children }) => {
  const intervalRefs = useRef({});
  const { profile } = useContext(AuthContext);
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const [jobs, setJobs] = useState(() => {
    const storedJobs = JSON.parse(localStorage.getItem("jobs")) || {};
    const tryCount = Number(localStorage.getItem("jobs-try-count")) || 0;
    return {
      tryCount: tryCount || 0,
      bulletin: storedJobs.bulletin || false,
      download: storedJobs.download || false,
    };
  });
  const { step } = useBulletinStore((state) => state.bulletin);

  const [newNotif, setNewNotif] = useState(false);
  const [totalNotif, setTotalNotif] = useState(0);
  const [totalAnnounce, setTotalAnnounce] = useState(0);

  const fetchNotifications = useCallback(() => {
    notification
      .statistical()
      .then((res) => {
        setNewNotif(
          !!res.data.data.unread_announcement ||
            !!res.data.data.unread_notification,
        );
        setTotalAnnounce(res.data.data.unread_announcement);
        setTotalNotif(res.data.data.unread_notification);
      })
      .catch((err) => console.log(err));
  }, [profile]);

  useEffect(() => {
    if (!profile) return;
    fetchNotifications();
  }, [fetchNotifications]);

  const checkBulletinStatus = async (id) => {
    try {
      if (jobs.tryCount > TRY_LIMIT) return stopJob("bulletin");
      const { data } = await bulletin.getById(id, true);
      jobs.tryCount++;
      localStorage.setItem("jobs-try-count", String(jobs.tryCount));

      if (data?.data?.files?.length > 0) {
        const fileDate = new Date(
          data?.data?.files?.[data?.data?.files.length - 1]?.created_at,
        );
        if (fileDate.getTime() < new Date().getTime() - 60 * 1000) {
          return;
        }
        stopJob("bulletin");
        fetchNotifications();
        notif.success(
          "فایل بولتن آماده شد.",
          <CheckCircle className="text-light-success-text-rest" size={26} />,
        );
        if (step === 6) {
          setBulletin({
            step: 7,
            link: data?.data?.files?.[data?.data?.files.length - 1]?.filename,
          });
        }
      }
    } catch (e) {
      console.error(e);
    }
  };

  const startJob = (type, data) => {
    if (!jobs[type]) {
      const updatedJobs = { ...jobs, [type]: data };
      setJobs(updatedJobs);
      if (type === "bulletin") {
        localStorage.setItem("jobs", JSON.stringify(updatedJobs));
        intervalRefs.current[type] = setInterval(
          () => checkBulletinStatus(data),
          5000,
        );
      } else if (type === "download") {
        //todo: download logic
      }
    }
  };

  const stopJob = (type) => {
    for (let i = 1; i <= intervalRefs.current[type]; i++) {
      clearInterval(i);
    }
    const updatedJobs = { ...jobs, [type]: false };
    setJobs(updatedJobs);
    localStorage.setItem("jobs", JSON.stringify(updatedJobs));
    localStorage.setItem("jobs-try-count", "0");
  };

  useEffect(() => {
    if (jobs.bulletin) {
      intervalRefs.current.bulletin = setInterval(
        () => checkBulletinStatus(jobs.bulletin),
        5000,
      );
    }
    if (jobs.download) {
      // intervalRefs.current.download = setInterval(
      //   () => checkFileStatus("download", "/api/check-download-status"),
      //   5000,
      // );
    }

    return () => {
      Object.values(intervalRefs.current).forEach(clearInterval);
    };
  }, []);

  return (
    <CronJobContext.Provider
      value={{
        jobs,
        startJob,
        stopJob,
        newNotif,
        totalNotif,
        totalAnnounce,
        fetchNotifications,
      }}
    >
      {children}
    </CronJobContext.Provider>
  );
};

export default CronJobProvider;
