import { memo } from "react";
import fallbackImage from "/logo_small.png";

const NodeTypeCard = ({ img, title, isSelected = false, classname }) => {
  return (
    <div
      className={`select-none grid items-center justify-center gap-3 py-5 px-2 border rounded-lg ${classname} ${
        isSelected
          ? "bg-light-primary-background-highlight"
          : "bg-light-neutral-background-medium"
      } w-32 h-32 cursor-pointer transition-all`}
    >
      <img
        src={img || fallbackImage}
        onError={(e) => (e.target.src = fallbackImage)}
        width={50}
        className="mx-auto"
      />
      <p className="font-body-small text-center">{title}</p>
    </div>
  );
};

export default memo(NodeTypeCard);
