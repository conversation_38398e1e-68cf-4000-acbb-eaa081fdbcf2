import { useState, useEffect, memo } from "react";
import { Cloud, FunnelSimple, SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ReactWordcloud from "react-wordcloud";
import CLUSTER_COLORS from "constants/colors.js";
import "../../style.css";
import { preprocessWord } from "utils/helper";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import advanceSearch from "service/api/advanceSearch";
import { useReport360Store } from "store/report360Store";
import RepeatWordsChart from "./repeatWordsChart";
import { Tabs360 } from "./360tabs";
import { buildRequestData } from "utils/requestData";
import use360requestStore from "store/360requestStore";
import { toPersianNumber } from "utils/helper";
import useChartDataStore from "store/dataCacheStore";

const RepeatWords = () => {
  const { profile, date } = useReport360Store((state) => state.report);
  const profileCleared = Array.isArray(profile.user_name)
    ? profile.user_name
    : [profile.user_name || profile.key || profile.channel_username];
  const platform = profile.platform;
  const { updatePlatformContent } = use360requestStore();
  const sourceReport = use360requestStore((state) => state.report);
  const { chartData, setChartData } = useChartDataStore();
  const [categories, setCategories] = useState([]);
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("cluster");
  const [loading, setLoading] = useState(false);
  const [wordCloudData, setWordCloudData] = useState([]);
  const [info, setInfo] = useState([]);

  // Unique key for this chart's data
  const chartKey = `repeatWordsChart_${platform}_${
    profile.id || profile.user_name || profile.key || profile.channel_id
  }`;

  const fetchProfileData = async (abortController) => {
    const results = await Promise.all(
      profileCleared.map(async (field) => {
        const filters = {
          date,
          platform,
          sources:
            platform === "twitter"
              ? [field] || []
              : field?.id
              ? [field.id.toString()]
              : profile?.channel_id
              ? [profile?.channel_id]
              : [],
        };

        const infoQuery = buildRequestData(filters, "cloud");
        const response = await advanceSearch.search(infoQuery, abortController);

        return response.data.data?.[platform];
      })
    );
    return results;
  };

  const updateState = (result) => {
    const innerArray = result[0];

    if (Array.isArray(innerArray)) {
      const updatedWordClouds =
        innerArray.map(({ key, count }) => ({
          text: preprocessWord(key), // Assuming preprocessWord is defined
          value: count,
        })) || [];
      setWordCloudData(updatedWordClouds);

      setInfo(innerArray);

      setCategories(innerArray.slice(0, 10).map(({ key }) => key));

      const payload = innerArray.map(({ key, count }) => ({
        key,
        count,
      }));

      updatePlatformContent(platform, "frequent_words", payload);

      setChartData(chartKey, {
        wordCloudData: updatedWordClouds,
        info: innerArray,
        categories: innerArray.slice(0, 10).map(({ key }) => key),
      }); // Cache the data
    } else {
      console.warn("Expected an array inside `result[0]`.");
      setChartData(chartKey, {
        wordCloudData: [],
        info: [],
        categories: [],
      }); // Cache empty data
    }
  };

  const getData = async (abortController) => {
    setLoading(true);
    try {
      const result = await fetchProfileData(abortController);

      if (!abortController.signal.aborted) {
        updateState(result);
      }
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error("Error fetching data:", error);
        setWordCloudData([]);
        setInfo([]);
        setCategories([]);
        setChartData(chartKey, {
          wordCloudData: [],
          info: [],
          categories: [],
        }); // Cache empty data
      }
    } finally {
      if (!abortController.signal.aborted) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    // Check if data is cached and not updating
    // if (chartData[chartKey] && !isUpdate) {
    //   setWordCloudData(chartData[chartKey].wordCloudData);
    //   setInfo(chartData[chartKey].info);
    //   setCategories(chartData[chartKey].categories);
    //   setLoading(false);
    // } else if (!isUpdate) {
    //   // setWordCloudData(new Array(profileCleared?.length).fill([]));

    // }
    getData();
  }, [chartKey, date, profile, chartData]);

  useEffect(() => {
    // if (isUpdate) {
    //   const platformData =
    //     sourceReport.source.platform[platform]?.resource_overview
    //       .frequent_words || [];
    //   const transformedWords = platformData.map(({ key, count }) => ({
    //     text: preprocessWord(key),
    //     value: count,
    //   }));
    //   const newInfo = platformData.map(({ key, count }) => ({
    //     word: preprocessWord(key),
    //     count,
    //   }));
    //   const newCategories = platformData.map(({ key }) => preprocessWord(key));
    //   setWordCloudData(transformedWords);
    //   setInfo(newInfo);
    //   setCategories(newCategories);
    //   setChartData(chartKey, {
    //     wordCloudData: transformedWords,
    //     info: newInfo,
    //     categories: newCategories,
    //   }); // Cache updated data
    // }
  }, [sourceReport, chartKey]);

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  const getGreenColor = (count) => {
    const maxCount = Math.max(...info.map((item) => item.count));
    const minCount = Math.min(...info.map((item) => item.count));

    const intensity = Math.floor(
      ((maxCount - count) / (maxCount - minCount)) * 155
    );

    return `rgb(${30 + intensity}, 200, ${30 + intensity})`;
  };

  return (
    <div className="flex !h-full">
      <Card className="px-0 card-animation card-delay !h-full">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row gap-2 w-full justify-between">
            <div className="w-1/2">
              <Title title="کلمات پر تکرار" />
            </div>
            <div className="flex w-1/2">
              <Tabs360
                tabArray={[
                  { id: "wordCloud", title: "ابر کلمات", icon: Cloud },
                  { id: "cluster", title: "نمودار", icon: FunnelSimple },
                ]}
                activeTab={wordCloudActiveTab}
                onChange={onClusterTabChange}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 !h-full">
            {loading ? (
              <div className="flex w-full h-full justify-center items-center">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            ) : info.length ? (
              <>
                {wordCloudActiveTab === "wordCloud" && info.length > 0 && (
                  <div className="flex flex-1 responsive-svg h-[26rem]">
                    <ReactWordcloud
                      options={{
                        rotations: 1,
                        rotationAngles: [0],
                        enableTooltip: true,
                        deterministic: false,
                        fontFamily: "iranyekan",
                        fontSizes: [14, 54],
                        padding: 10,
                        colors: CLUSTER_COLORS,
                        tooltipOptions: { theme: "light", arrow: true },
                      }}
                      words={wordCloudData}
                      callbacks={{
                        getWordTooltip: (word) => {
                          return `${word.text} (${toPersianNumber(
                            word.value
                          )})`;
                        },
                      }}
                    />
                  </div>
                )}
                {wordCloudActiveTab === "cluster" && info.length > 0 && (
                  <RepeatWordsChart
                    colors={info.map((item) => getGreenColor(item.count))}
                    info={info}
                    categories={categories}
                    loading={loading}
                  />
                )}
              </>
            ) : (
              <div className="h-full flex items-center justify-center font-subtitle-medium">
                داده ای برای نمایش وجود ندارد
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};
export default memo(RepeatWords);
