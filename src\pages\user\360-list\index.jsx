import { useContext, useEffect, useRef, useState } from "react";
import { CButton } from "components/ui/CButton";
import {
  CaretLeft,
  Info,
  Plus,
  Warning,
  SpinnerGap,
  MagnifyingGlass,
} from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import { Card } from "components/ui/Card";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { useReport360Store } from "store/report360Store.js";
import Drawer from "components/Drawer";
import Info360 from "./components/Info360";
import Card360 from "./components/Card360";
import AuthContext from "context/auth-context";
import { notification, toPersianNumber } from "utils/helper";
import { ToastContainer } from "react-toastify";
import { CInput } from "components/ui/CInput";
import { AnimatePresence, motion } from "framer-motion";

const Report360List = () => {
  const [showMore, setShowMore] = useState(false);
  const [showReportDropdown, setShowReportDropdown] = useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [order, setOrder] = useState("asc");
  const [reportTypes, setReportTypes] = useState([]);
  const [reportLength, setReportLength] = useState(null);
  const [searchValue, setSearchValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [isInputVisible, setIsInputVisible] = useState(false);
  const navigate = useNavigate();
  useBreadcrumb([{ title: "گزارش ۳۶۰" }]);
  const resetReport = useReport360Store((state) => state.resetReport);
  const { profile } = useContext(AuthContext);
  const access360 = profile?.limitations?.report_360_access;

  const reportDropdownRef = useRef(null);
  const sortDropdownRef = useRef(null);
  const searchContainerRef = useRef(null);
  const inputRef = useRef(null);

  const handleReportTypeChange = (type) => {
    setReportTypes((prev) => {
      if (prev.includes(type)) {
        return prev.filter((t) => t !== type);
      } else {
        return [...prev, type];
      }
    });
  };

  const handleSortChange = (type) => {
    setOrder(type);
    setShowSortDropdown(false);
  };

  const handleSearch = () => {
    if (searchValue.trim()) {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    } else {
      setSearchValue("");
    }
  };

  const handleIconClick = () => {
    setIsInputVisible(true);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        reportDropdownRef.current &&
        !reportDropdownRef.current.contains(event.target)
      ) {
        setShowReportDropdown(false);
      }
      if (
        sortDropdownRef.current &&
        !sortDropdownRef.current.contains(event.target)
      ) {
        setShowSortDropdown(false);
      }
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target)
      ) {
        setIsInputVisible(false);
        setSearchValue("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleDataFromCard360 = (data) => {
    setReportLength(data?.length);
  };

  const translator = {
    source: "منبع",
    entity: "موجودیت",
    location: "مکان",
  };

  const getDisplayText = () => {
    if (reportTypes.length === 0) return "همه";
    if (reportTypes.length === 3) return "همه";
    return reportTypes.map((type) => translator[type]).join(", ");
  };

  return (
    <>
      <div className="p-6 flex flex-col gap-4 h-f">
        <Card className="flex w-full">
          <div className="flex flex-col w-full gap-4">
            <div className="flex justify-end">
              <div className="flex items-center gap-8 w-full">
                <div className="flex gap-8 justify-between">
                  <div
                    className="relative flex items-center"
                    ref={reportDropdownRef}
                  >
                    <div
                      className="flex items-center gap-5 cursor-pointer"
                      onClick={() => setShowReportDropdown(!showReportDropdown)}
                    >
                      <span className="font-body-medium">انواع گزارش:</span>
                      <div className="flex text-[#878787] gap-2 items-center">
                        <span className="font-body-medium">
                          {getDisplayText()}
                        </span>
                        <CaretLeft />
                      </div>
                    </div>
                    {showReportDropdown && (
                      <div className="absolute top-full right-0 bg-white shadow-lg rounded-lg mt-2 w-40 p-2 !z-10">
                        <label className="flex items-center cursor-pointer gap-3 p-2 hover:bg-gray-100">
                          <input
                            type="checkbox"
                            className="mr-2"
                            checked={reportTypes.includes("source")}
                            onChange={() => handleReportTypeChange("source")}
                          />
                          منبع
                        </label>
                        <label className="flex items-center cursor-pointer gap-3 p-2 hover:bg-gray-100">
                          <input
                            type="checkbox"
                            className="mr-2"
                            checked={reportTypes.includes("entity")}
                            onChange={() => handleReportTypeChange("entity")}
                          />
                          موجودیت
                        </label>
                        <label className="flex items-center cursor-pointer gap-3 p-2 hover:bg-gray-100">
                          <input
                            type="checkbox"
                            className="mr-2"
                            checked={reportTypes.includes("location")}
                            onChange={() => handleReportTypeChange("location")}
                          />
                          مکان
                        </label>
                      </div>
                    )}
                  </div>

                  <div
                    className="relative flex items-center"
                    ref={sortDropdownRef}
                  >
                    <div
                      className="flex items-center gap-5 cursor-pointer"
                      onClick={() => setShowSortDropdown(!showSortDropdown)}
                    >
                      <span className="font-body-medium">ترتیب نمایش:</span>
                      <div className="flex text-[#878787] gap-2 items-center">
                        <span className="font-body-medium">
                          {order === "asc" ? "صعودی" : "نزولی"}
                        </span>
                        <CaretLeft />
                      </div>
                    </div>
                    {showSortDropdown && (
                      <div className="absolute top-full right-0 bg-white shadow-lg rounded-lg mt-2 w-40 !z-10 p-2">
                        <p
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={() => handleSortChange("desc")}
                        >
                          نزولی
                        </p>
                        <p
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={() => handleSortChange("asc")}
                        >
                          صعودی
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="w-[30%] relative" ref={searchContainerRef}>
                  <AnimatePresence mode="wait">
                    {isInputVisible ? (
                      <motion.div
                        key="input"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        onAnimationComplete={() => {
                          inputRef.current.focus();
                        }}
                      >
                        <CInput
                          id={"q"}
                          name={"q"}
                          inset={true}
                          size={"sm"}
                          headingIcon={<MagnifyingGlass />}
                          validation={"none"}
                          direction={"rtl"}
                          placeholder={"عنوان گزارش را جست‌و‌جو کنید"}
                          onChange={(e) => setSearchValue(e.target.value)}
                          value={searchValue}
                          className={"flex-1 !mb-0"}
                          inputProps={{ ref: inputRef }}
                          onKeyPress={(e) => {
                            if (e.key === "Enter") {
                              handleSearch();
                            }
                          }}
                        />
                      </motion.div>
                    ) : (
                      <motion.div
                        key="icon"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className="cursor-pointer text-light-neutral-text-medium"
                        onClick={handleIconClick}
                      >
                        <MagnifyingGlass size={18} />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="[direction:ltr] w-[200px]">
                  <CButton
                    rightIcon={<Plus />}
                    width={155}
                    className={"[direction:ltr]"}
                    mode="outline"
                    onClick={() => {
                      if (access360 === reportLength) {
                        notification.error(
                          `کاربر گرامی، محدودیت تعداد گزارش‌های ${toPersianNumber(
                            360
                          )} شما به پایان رسیده است.`,
                          <Warning
                            size={25}
                            className="text-light-error-text-rest"
                          />
                        );
                      } else {
                        resetReport();
                        navigate("/app/report-360/create/step-1");
                      }
                    }}
                  >
                    گزارش جدید
                  </CButton>
                </div>
                <div className="w-[155px]" onClick={() => setShowMore(true)}>
                  <CButton
                    mode="outline"
                    size="sm"
                    role="neutral"
                    className="gap-2 !h-10"
                  >
                    <Info
                      size={17}
                      className="text-light-neutral-text-medium"
                    />
                    <p className="text-light-neutral-text-medium font-button-medium">
                      گزارش ۳۶۰ چیست
                    </p>
                  </CButton>
                </div>
              </div>
            </div>
          </div>
        </Card>
        {loading ? (
          <div className="flex w-full h-full justify-center pt-52 items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        ) : (
          <Card360
            onData={handleDataFromCard360}
            order={order}
            reportType={reportTypes}
            searchQuery={searchValue.trim()}
          />
        )}
        {showMore && (
          <Drawer setShowMore={setShowMore}>
            <Info360 />
          </Drawer>
        )}
      </div>
      <ToastContainer />
    </>
  );
};

export default Report360List;
