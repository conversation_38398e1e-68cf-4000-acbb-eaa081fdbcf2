import { useEffect, useRef } from "react";
import * as d3 from "d3";
import { Card } from "components/ui/Card";
import { toPersianNumber } from "utils/helper";
import ExportMenu from "components/ExportMenu/index.jsx";
import Title from "pages/user/compare-create/components/step-two/charts/Title";

const RadialClusterChart = ({
  active,
  data,
  setSelectedNode,
  containerRef,
}) => {
  const chartRef = useRef();

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Impact (%)",
      data: (data?.trends || []).map(
        (trend) => (trend.stats.impact || 0) * 100
      ),
      time: (data?.trends || []).map((trend) => trend.title),
    },
    {
      name: "Frequency",
      data: (data?.trends || []).map((trend) => trend.stats.posts || 0),
      time: (data?.trends || []).map((trend) => trend.title),
    },
  ];

  const time = (data?.trends || []).map((trend) => trend.title);

  const transformData = () => {
    const trendCategories = {
      Potential: "موج‌های بالقوه",
      Increasing: "موج‌های افزایشی",
      Decreasing: "موج‌های کاهشی",
      Frequent: "موج‌های فراگیر",
    };

    const categoriesType = {
      Potential: "بالقوه",
      Increasing: "افزایشی",
      Decreasing: "کاهشی",
      Frequent: "فراگیر",
    };

    const fullData = {
      name: "ریشه",
      children: Object.entries(
        (data?.trends || []).reduce((acc, trend) => {
          const category = trendCategories[trend.trend_type] || "سایر";
          if (!acc[category]) {
            acc[category] = [];
          }
          acc[category].push({
            name: trend.title,
            impact: trend.stats.impact || 0,
            type: categoriesType[trend.trend_type] || "",
            frequency: trend.stats.posts || 0,
            children: trend.elements.slice(0, 5).map((element) => ({
              name: element.content,
            })),
          });
          return acc;
        }, {})
      ).map(([name, children]) => ({ name, children })),
    };

    return {
      name: "موج",
      children:
        active === "همه موج‌ها"
          ? fullData.children
          : fullData.children.filter((child) => child.name === active),
    };
  };

  const handleTrendClick = (event, d) => {
    if (d?.data?.name) {
      setSelectedNode(d.data.name);
    }
  };
  useEffect(() => {
    if (!data || !data.trends || !Array.isArray(data.trends)) {
      console.error("Invalid or missing data:", data);
      return;
    }
    const filteredData = transformData();
    if (!filteredData.children || filteredData.children.length === 0) {
      console.error("No valid children in filtered data:", filteredData);
      return;
    }

    const width = 1150;
    const height = 839;
    const cx = width * 0.5;
    const cy = height * 0.5;
    const radius = Math.min(width, height) / 2 - 30;
    const tree = d3
      .cluster()
      .size([2 * Math.PI, radius])
      .separation((a, b) => (a.parent === b.parent ? 1 : 3) / a.depth);

    let root;
    try {
      root = tree(
        d3
          .hierarchy(filteredData)
          .sort((a, b) => d3.ascending(a.data.name, b.data.name))
      );
    } catch (error) {
      console.error("Error creating hierarchy:", error);
      return;
    }

    d3.select(chartRef.current).select("svg").remove();
    d3.select(chartRef.current).select(".tooltip").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", [-cx, -cy, width, height])
      .attr("style", "width: 100%; height: auto; font: 12px Vazir;");

    const tooltip = d3
      .select(chartRef.current)
      .append("div")
      .attr("class", "tooltip")
      .style("position", "absolute")
      .style("background", "white")
      .style("border", "1px solid #ccc")
      .style("padding", "5px 10px")
      .style("border-radius", "4px")
      .style("box-shadow", "0 2px 4px rgba(0,0,0,0.2)")
      .style("font-family", "iranyekan")
      .style("font-size", "12px")
      .style("direction", "rtl")
      .style("pointer-events", "none")
      .style("opacity", 0);

    const trendColors = {
      "موج‌های بالقوه": "#ff7f0e",
      "موج‌های افزایشی": "#d62728",
      "موج‌های کاهشی": "#1f77b4",
      "موج‌های فراگیر": "#04a286",
    };

    svg
      .append("g")
      .attr("fill", "none")
      .attr("stroke-opacity", 0.4)
      .attr("stroke-width", 1.5)
      .selectAll("path")
      .data(root.links())
      .join("path")
      .attr("stroke", (d) => {
        try {
          let categoryNode = d.source;
          while (categoryNode && categoryNode.depth > 1) {
            categoryNode = categoryNode.parent;
          }
          const categoryName = categoryNode?.data?.name || "";
          return trendColors[categoryName] || "#555";
        } catch (error) {
          console.error("Error assigning link color:", error);
          return "#555";
        }
      })
      .attr("d", (d) => {
        const linkGen = d3
          .linkRadial()
          .angle((d) => d.x)
          .radius((d) => d.y);
        return linkGen({ source: d.source, target: d.source });
      })
      .transition()
      .duration(1000)
      .delay((d, i) => i * 10)
      .attr(
        "d",
        d3
          .linkRadial()
          .angle((d) => d.x)
          .radius((d) => d.y)
      );

    svg
      .append("g")
      .selectAll("circle")
      .data(root.descendants())
      // .join("circle")
      .attr("r", 0)
      .attr(
        "transform",
        (d) => `rotate(${(d.x * 180) / Math.PI - 90}) translate(${d.y},0)`
      )
      .attr("fill", (d) => (d.depth === 2 ? "red" : "#555"))
      .style("cursor", (d) => (d.depth === 2 ? "pointer" : "default"))
      .transition()
      .duration(800)
      .delay((d, i) => i * 10 + 500)
      .attr("r", 4)
      .on("end", function () {
        d3.select(this)
          .on("click", (event, d) => {
            if (d.depth === 2) {
              handleTrendClick(event, d);
            }
          })
          .on("mouseover", (event, d) => {
            if (d.depth === 2) {
              d3.select(event.currentTarget).attr("fill", "blue");
              tooltip
                .style("opacity", 1)
                .html(
                  `
                  <div style="display: flex; align-items: center; gap: 4px;">
                    <b>${d.data.name}</b>
                  </div>
                  <div style="display: flex; align-items: center; gap: 4px;">
                    <p>تاثیرگذاری: ${toPersianNumber(
                      ((d.data.impact || 0) * 100).toFixed(0)
                    )}%</p>
                  </div>
                  <div style="display: flex; align-items: center; gap: 4px;">
                    <p>فراگیری: ${toPersianNumber(d.data.frequency || 0)}</p>
                  </div>
                `
                )
                .style("left", `${event.pageX + 10}px`)
                .style("top", `${event.pageY - 10}px`);
            }
          })
          .on("mousemove", (event) => {
            tooltip
              .style("left", `${event.pageX + 10}px`)
              .style("top", `${event.pageY - 10}px`);
          })
          .on("mouseout", (event, d) => {
            if (d.depth === 2) {
              d3.select(event.currentTarget).attr("fill", "red");
              tooltip.style("opacity", 0);
            }
          });
      });

    svg
      .append("g")
      .attr("stroke-linejoin", "round")
      .attr("stroke-width", 3)
      .selectAll("text")
      .data(root.descendants())
      .join("text")
      .attr(
        "transform",
        (d) =>
          `rotate(${(d.x * 180) / Math.PI - 90}) translate(${d.y},0) rotate(${
            d.x >= Math.PI ? 180 : 0
          })`
      )
      .attr("dy", "0.31em")
      .attr("x", (d) => (d.x < Math.PI === !d.children ? 8 : -8))
      .attr("text-anchor", (d) =>
        d.x < Math.PI === !d.children ? "start" : "end"
      )
      .attr("paint-order", "stroke")
      .attr("stroke", "white")
      .attr("fill", "black")
      .attr("font-size", (d) => (d.depth === 2 ? "14px" : "12px"))
      .attr("direction", "rtl")
      .style("cursor", (d) => (d.depth === 2 ? "pointer" : "default"))
      .style("opacity", 0)
      .text((d) => {
        const maxLength = 15;
        return d.data.name.length > maxLength
          ? d.data.name.substring(0, maxLength - 3) + "..."
          : d.data.name;
      })
      .transition()
      .duration(800)
      .delay((d, i) => i * 10 + 1000)
      .style("opacity", 1)
      .on("end", function (event, d) {
        d3.select(this)
          .on("click", (event, d) => {
            if (d.depth === 2) {
              handleTrendClick(event, d);
            }
          })
          .on("mouseover", function (event, d) {
            if (d.depth === 2) {
              d3.select(this)
                .style("font-size", "16px")
                .style("fill", "#1f77b4");
              tooltip
                .style("opacity", 1)
                .html(
                  `
                  <div style="display: flex; align-items: center; gap: 4px;">
                    <b>${d.data.name}</b>
                  </div>
                  <div style="display: flex; align-items: center; gap: 4px;">
                    <p>تاثیرگذاری: ${toPersianNumber(
                      ((d.data.impact || 0) * 100).toFixed(0)
                    )}%</p>
                  </div>
                  <div style="display: flex; align-items: center; gap: 4px;">
                    <p>فراگیری: ${toPersianNumber(d.data.frequency || 0)}</p>
                  </div>
                `
                )
                .style("left", `${event.pageX + 10}px`)
                .style("top", `${event.pageY - 10}px`);
            }
          })
          .on("mousemove", (event) => {
            tooltip
              .style("left", `${event.pageX + 10}px`)
              .style("top", `${event.pageY - 10}px`);
          })
          .on("mouseout", function (event, d) {
            if (d.depth === 2) {
              d3.select(this).style("font-size", "14px").style("fill", "black");
              tooltip.style("opacity", 0);
            }
          });
      });

    return () => {
      d3.select(chartRef.current).selectAll("*").remove();
    };
  }, [active, data, setSelectedNode]);

  return (
    <Card className="radial-cluster-chart-container" ref={containerRef}>
      <div className="flex">
        <div className="bg-white pr-4" ref={chartRef}></div>
        <ExportMenu
          chartSelector=".radial-cluster-chart-container"
          fileName="radial-cluster-chart"
          series={series}
          time={time}
          excelHeaders={["Trend", "Impact (%)", "Frequency"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
          customExcelFormat="radialCluster"
          data={data}
        />
      </div>
    </Card>
  );
};

export default RadialClusterChart;
