import { ChartDonut, ListBullets } from "@phosphor-icons/react";
import PropTypes from "prop-types";

const TopMenuTabs = ({ activeTab, setActiveTab }) => {
  return (
    <div className="flex items-center font-body-large gap-4">
      <p
        key={"table"}
        className={`flex items-center gap-1 cursor-pointer p-1 ${
          "table" === activeTab
            ? "border-b-2 border-light-primary-text-rest text-light-neutral-text-high"
            : "text-light-neutral-text-medium"
        }`}
        onClick={() => setActiveTab("table")}
      >
        <ListBullets size={18} />
        لیست گزارش‌های 360
      </p>
      <p
        key={"report"}
        className={`flex items-center gap-1 cursor-pointer p-1 ${
          "report" === activeTab
            ? "border-b-2 border-light-primary-text-rest text-light-neutral-text-high"
            : "text-light-neutral-text-medium"
        }`}
        onClick={() => setActiveTab("report")}
      >
        <ChartDonut size={18} />
        گزارش آماری
      </p>
    </div>
  );
};

TopMenuTabs.propTypes = {
  activeTab: PropTypes.string.isRequired,
  setActiveTab: PropTypes.func.isRequired,
};

export default TopMenuTabs;
