import { useEffect, useState, memo } from "react";
import MultipleBar from "./MultipleBar";
import { Card } from "components/ui/Card";
import { SpinnerGap } from "@phosphor-icons/react";
import "../../style.css";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import { useReport360Store } from "store/report360Store";
import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";
import { SUBJECT_CATEGORIES } from "constants/subject-category.js";
import use360requestStore from "store/360requestStore";
import PropTypes from "prop-types";
import useChartDataStore from "store/dataCacheStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const ThematicCategory = ({ isUpdate }) => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    categories: state.report?.content?.report_info?.categoreis,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );
  const [categories, setCategories] = useState([]);
  const [localData, setLocalData] = useState([]);
  const [loading, setLoading] = useState(false);

  // Prepare series and time for ExportMenu
  const series = localData.map((serie) => ({
    name: serie.name,
    data: serie.data,
    time: categories,
  }));

  const time = categories;

  const getData = async () => {
    if (loading) return;
    setLoading(true);
    try {
      const filters = {
        date,
        platform: sourceReport.platform,
      };

      sourceReport.platform === "news"
        ? (filters["sources"] = sourceReport.profile?.id
            ? [sourceReport.profile?.id?.toString()]
            : [])
        : (filters["sources"] = sourceReport.profile?.user_name
            ? [sourceReport.profile?.user_name]
            : sourceReport.profile?.key
            ? [sourceReport.profile?.key]
            : sourceReport.profile?.channel_id
            ? [sourceReport.profile?.channel_id]
            : []);

      const request = buildRequestData(filters, "categories");
      const response = await advanceSearch.search(request);

      const data = response?.data?.data || [];

      const transformedData = data[sourceReport.platform].map((item) => ({
        key: item.key,
        count: item.count,
      }));

      updateReportField("content.report_info.categories", transformedData);

      const newCategories = transformedData.map(
        (item) =>
          SUBJECT_CATEGORIES.find((category) => category.value === item.key)
            ?.label
      );

      const newChartData = [
        {
          name: "Count",
          data: transformedData.map((item) =>
            parseFloat(((item.count / data.total) * 100).toFixed(2))
          ),
          color: "#7d6cd5",
        },
      ];

      setCategories(newCategories);
      setLocalData(newChartData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setCategories([]);
      setLocalData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log("Categories");
    console.log(sourceReport);

    // if (
    //   sourceReport.categories &&
    //   Array.isArray(sourceReport.categories) &&
    //   sourceReport.categories.length &&
    //   sourceReport.date.from === date.from &&
    //   sourceReport.date.to === date.to
    // ) {
    //   const newCategories = sourceReport.categories.map(
    //     (item) =>
    //       SUBJECT_CATEGORIES.find((category) => category.value === item.key)
    //         ?.label
    //   );

    //   const newChartData = [
    //     {
    //       name: "Count",
    //       data: sourceReport.categories.map((item) =>
    //         parseFloat(((item.count / data.total) * 100).toFixed(2))
    //       ),
    //       color: "#7d6cd5",
    //     },
    //   ];

    //   setCategories(newCategories);
    //   setLocalData(newChartData);
    // } else {
    //   getData();
    //   updateReportField("start_date", date.from);
    //   updateReportField("end_date", date.to);
    // }

    getData();
  }, [isUpdate, date]);

  // useEffect(() => {
  //   if (isUpdate && platformData) {
  //     const totalCount = platformData.reduce(
  //       (sum, item) => sum + item.count,
  //       0
  //     );

  //     const transformedData = platformData.map((item) => {
  //       const category = SUBJECT_CATEGORIES.find(
  //         (cat) => cat.value === item.key
  //       );
  //       return {
  //         key: item.key,
  //         label: category ? category.label : item.key,
  //         count: item.count,
  //       };
  //     });

  //     const newCategories = transformedData.map((item) => item.label);

  //     const newChartData = [
  //       {
  //         name: "Count",
  //         data: transformedData.map((item) =>
  //           parseFloat(((item.count / totalCount) * 100).toFixed(2))
  //         ),
  //         color: "#7d6cd5",
  //       },
  //     ];

  //     // Avoid unnecessary updates
  //     if (
  //       JSON.stringify(newCategories) !== JSON.stringify(categories) ||
  //       JSON.stringify(newChartData) !== JSON.stringify(localData)
  //     ) {
  //       setCategories(newCategories);
  //       setLocalData(newChartData);
  //       setChartData(chartKey, {
  //         categories: newCategories,
  //         data: newChartData,
  //         total: totalCount,
  //       });
  //     }
  //   }
  // }, [isUpdate, platformData, chartKey, categories, localData]);

  return (
    <div className="flex w-full h-full">
      <Card className="!p-6 card-animation card-delay">
        <div className="flex flex-col gap-6 w-full">
          <div className="flex items-center justify-between">
            <Title title="دسته‌بندی موضوعی" />
            <ExportMenu
              chartSelector=".thematic-category-container"
              fileName="thematic-category"
              series={series}
              time={time}
              excelHeaders={["Category", "Percentage"]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
              chartTitle="دسته‌بندی موضوعی"
            />
          </div>
          <div className="thematic-category-container">
            {loading ? (
              <div className="w-full h-[400px] flex justify-center items-center">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            ) : (
              <>
                {localData?.length &&
                localData.some((item) => item.data?.length) ? (
                  <MultipleBar categories={categories} data={localData} />
                ) : (
                  <div className="h-[430px] flex items-center justify-center font-subtitle-medium">
                    داده‌ای برای نمایش وجود ندارد
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

ThematicCategory.propTypes = {
  isUpdate: PropTypes.bool,
};

export default memo(ThematicCategory);
