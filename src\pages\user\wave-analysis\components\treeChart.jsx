import { useEffect } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import Treemap from "highcharts/modules/treemap";
import { toPersianNumber } from "utils/helper.js";
import { Card } from "components/ui/Card";
import ExportMenu from "components/ExportMenu/index.jsx";
import Title from "pages/user/compare-create/components/step-two/charts/Title";

Treemap(Highcharts);

const TreeMap = ({ active, data, setSelectedNode, containerRef }) => {
  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Frequency",
      data: (data?.trends || []).map((trend) => trend.stats.posts || 0),
      time: (data?.trends || []).map((trend) => trend.title),
    },
  ];

  const time = (data?.trends || []).map((trend) => trend.title);

  const transformData = () => {
    const trendCategories = {
      Potential: "موج‌های بالقوه",
      Increasing: "موج‌های افزایشی",
      Decreasing: "موج‌های کاهشی",
      Frequent: "موج‌های فراگیر",
    };

    const categoryColors = {
      "موج‌های بالقوه": "#E9A506",
      "موج‌های افزایشی": "#F60000",
      "موج‌های کاهشی": "#1371F4",
      "موج‌های فراگیر": "#04A286",
    };

    const categorized = (data?.trends || []).reduce((acc, trend) => {
      const category = trendCategories[trend.trend_type] || "سایر";
      if (!acc[category]) acc[category] = [];
      acc[category].push({
        name: trend.title,
        value: trend.stats.posts || 0,
        id: `child_${trend.id}`,
        parent: category,
        color: categoryColors[category],
      });
      return acc;
    }, {});

    const dataArray = [];
    Object.entries(categorized).forEach(([category, children], index) => {
      dataArray.push({
        id: `parent_${index}`,
        name: category,
        value: null,
        color: categoryColors[category],
      });
      children.forEach((child) => {
        dataArray.push({
          id: child.id,
          name: child.name,
          value: child.value,
          parent: `parent_${index}`,
          color: child.color,
        });
      });
    });

    return active === "همه موج‌ها"
      ? dataArray
      : dataArray.filter(
          (item) =>
            item.name === active ||
            dataArray.some(
              (parent) => parent.id === item.parent && parent.name === active
            )
        );
  };

  const options = {
    chart: {
      type: "treemap",
      height: "642px",
      width: null,
    },
    exporting: {
      enabled: false, // Disable Highcharts default export button
    },
    tooltip: {
      headerFormat: "",
      backgroundColor: "#efefef",
      textAlign: "center",
      useHTML: true,
      shadow: false,
      formatter: function () {
        return `<div style="display:flex;gap:8px;padding:4px;font-family:iranyekan,serif;font-size:12px"> 
                <span>${toPersianNumber(this.point.value)}</span><span>:${
          this.key
        }</span>
        </div>`;
      },
    },
    breadcrumbs: {
      showFullPath: true,
      events: {
        click: function (event) {
          setTimeout(() => {
            if (event.newLevel === 0) {
              setSelectedNode("all");
            }
          }, 100);
        },
      },
    },
    series: [
      {
        name: "همه موج‌ها",
        type: "treemap",
        layoutAlgorithm: "squarified",
        allowDrillToNode: true,
        animationLimit: 1000,
        dataLabels: {
          enabled: false,
        },
        levels: [
          {
            level: 1,
            layoutAlgorithm: "sliceAndDice",
            dataLabels: {
              enabled: true,
              style: {
                fontSize: "14px",
                color: "white",
                fontFamily: "iranyekan",
                textOutline: "none",
                width: "100%",
                height: "100%",
              },
            },
            borderWidth: 3,
            levelIsConstant: false,
          },
          {
            level: 1,
            dataLabels: {
              style: {
                fontSize: "14px",
                color: "white",
                fontFamily: "iranyekan",
                textOutline: "none",
              },
            },
          },
        ],
        accessibility: {
          exposeAsGroupOnly: true,
        },
        data: transformData(),
      },
    ],
    subtitle: {
      text: null,
    },
    title: {
      text: null,
    },
    credits: {
      enabled: false,
    },
    plotOptions: {
      series: {
        cursor: "pointer",
        events: {
          click: function (event) {
            const pointId = event.point.id.split("_");
            if (pointId.length === 2) {
              setSelectedNode(event.point.name);
            } else if (pointId.length === 3) {
              setSelectedNode(event.point.name);
            }
          },
        },
      },
    },
  };

  useEffect(() => {
    const handleResize = () => {
      if (Highcharts.charts[0]) {
        Highcharts.charts[0].reflow();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [data, active, setSelectedNode]);

  return (
    <div ref={containerRef}>
      <Card className="treemap-container">
        <div className="w-full treemap-container">
          <HighchartsReact highcharts={Highcharts} options={options} />
        </div>

        <ExportMenu
          chartSelector=".treemap-container"
          fileName="treemap"
          series={series}
          time={time}
          excelHeaders={["Trend", "Frequency"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
          customExcelFormat="radialCluster"
          data={data}
        />
      </Card>
    </div>
  );
};

export default TreeMap;
