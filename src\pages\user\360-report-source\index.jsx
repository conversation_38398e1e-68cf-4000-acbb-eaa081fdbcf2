import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import Step3Profile from "./step-3-profile";
import { useReport360Store } from "store/report360Store.js";
import { useLocation } from "react-router-dom";
import { ToastContainer } from "react-toastify";

const Report360Source = () => {
  const { type, profile } = useReport360Store((state) => state.report);
  const location = useLocation();
  let breadcrumbConditional =
    location?.pathname?.split("/").at(-1) === "edit"
      ? [
          { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
          {
            title:
              type === "profile"
                ? "استعلام منبع"
                : type === "topic"
                ? "استعلام موجودیت"
                : "استعلام مکان محور",
            link: "/app/report-360/list",
          },
          { title: profile.title || "جست‌وجو" },
        ]
      : [
          { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
          { title: "گزارش جدید" },
        ];
  const breadcrumbList = breadcrumbConditional;
  useBreadcrumb(breadcrumbList);

  return (
    <div className="flex-1">
      <Step3Profile />
    </div>
  );
};

export default Report360Source;
