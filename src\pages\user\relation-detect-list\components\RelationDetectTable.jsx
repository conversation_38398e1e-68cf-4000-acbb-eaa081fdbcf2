import { useNavigate } from "react-router-dom";
import { parseTimeToPersian } from "utils/helper";
import { ToastContainer } from "react-toastify";

const TicketsTable = ({ data }) => {
  const navigate = useNavigate();
  return (
    <>
      <div
        className="grid grid-cols-5 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer"
        onClick={() => navigate(`/app/ticket/list/${data?.id}`)}
      >
        <div className="flex font-body-medium">{data?.title}</div>
        <div className="font-body-medium">
          {parseTimeToPersian(data?.updated_at)}
        </div>

        <div className="font-body-medium">
          <p>{parseTimeToPersian(data?.created_at)}</p>
        </div>
      </div>

      <ToastContainer />
    </>
  );
};

export default TicketsTable;
