import { useEffect } from "react";
import { useLayoutContext } from "../context/layout-context.jsx";

export const useBreadcrumb = (list) => {
  const { setBreadcrumb } = useLayoutContext();

  useEffect(() => {
    // Check if list is valid
    if (Array.isArray(list)) {
      setBreadcrumb(list);
    } else {
      console.warn("Breadcrumb list should be an array.");
    }
  }, []);

  return setBreadcrumb;
};
