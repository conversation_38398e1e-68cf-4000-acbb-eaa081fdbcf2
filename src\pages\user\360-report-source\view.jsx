import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import Step3Profile from "./step-3-profile";
import { useReport360Store } from "store/report360Store.js";
import { Link, useLocation, useParams } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { useEffect, useState } from "react";
import CardLoading from "components/ui/CardLoading.jsx";
import report360Servie from "service/api/report360";
import use360requestStore from "store/360requestStore";

const Report360SourceView = () => {
  const { id } = useParams();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState();
  const [title, setTitle] = useState("جست‌وجو");

  const setReport = useReport360Store((state) => state.setReport);
  const setReportData = use360requestStore((state) => state.setReportData);

  let breadcrumbConditional = [
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    {
      title: "استعلام منبع",
      link: "/app/report-360/list",
    },
    { title: title },
  ];
  const breadcrumbList = breadcrumbConditional;
  useBreadcrumb(breadcrumbList);

  const fetchReport = async () => {
    setLoading(true);
    try {
      const response = await report360Servie.getById(id);

      const data = response?.data?.data || null;
      if (!data) return setError("not-found");

      data.start_date = new Date(data.start_date);
      data.end_date = new Date(data.end_date);
      setTitle(data.q || "ناشناس");
      setReport({
        date: { from: data.start_date, to: data.end_date },
        id: id,
      });
      setReportData(data);
    } catch (e) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReport();
  }, [id]);

  if (error === "not-found") {
    return (
      <div className="w-full h-[640px] flex flex-col justify-center items-center">
        <div className="size-12 bg-[url(/empty_folder.png)] bg-no-repeat bg-contain bg-center mb-4"></div>
        <p className="font-body-large mb-2">گزارش مورد نظر یافت نشد!</p>
        <p className="font-body-medium">
          بازگشت به
          <Link
            to="/app/report-360/list"
            className="underline underline-offset-4 text-light-primary-text-rest cursor-pointer"
          >
            {" "}
            لیست گزارش‌ها{" "}
          </Link>
        </p>
      </div>
    );
  }
  if (loading)
    return (
      <div className="w-full h-[640px]">
        <CardLoading />
      </div>
    );
  return (
    <div className="flex-1">
      <Step3Profile isEdit={true} />
    </div>
  );
};

export default Report360SourceView;
