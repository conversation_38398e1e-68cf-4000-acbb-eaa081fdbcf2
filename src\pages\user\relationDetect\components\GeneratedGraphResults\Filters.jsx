import {
  ArrowCounterClockwise,
  CaretLeft,
  Check,
  FileArrowDown,
} from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import RangeSlider from "components/ui/RangeSlider";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { useState, useEffect, useRef } from "react";

const Filters = ({ printScreenHandler }) => {
  const [showGraphType, setShowGraphType] = useState({
    active: false,
    value: "پست",
  });
  const [connectionTypes, setConnectionTypes] = useState([]);
  const [connectionDropdownActive, setConnectionDropdownActive] =
    useState(false);
  const [weightDropdownActive, setWeightDropdownActive] = useState(false);
  const [RangeSliderVal, setRangeSliderVal] = useState(1);

  const graphTypeRef = useRef(null);
  const connectionRef = useRef(null);
  const weightRef = useRef(null);

  const toggleConnectionType = (type) => {
    setConnectionTypes((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
    );
  };

  const breadcrumbList = [
    { title: "شناسایی روابط", link: "/app/relation-detect/list" },
    { title: "گراف ارتباط سازی" },
  ];
  useBreadcrumb(breadcrumbList);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        !graphTypeRef.current?.contains(event.target) &&
        !connectionRef.current?.contains(event.target) &&
        !weightRef.current?.contains(event.target)
      ) {
        setShowGraphType((prev) => ({ ...prev, active: false }));
        setConnectionDropdownActive(false);
        setWeightDropdownActive(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="px-4">
      <div
        className="bg-light-neutral-surface-card rounded-lg p-3 h-full flex items-center justify-between font-button-medium"
        style={{
          boxShadow: "0px 2px 20px 0px #0000000D",
        }}
      >
        <div className="flex items-center gap-3 justify-center">
          <p>ارتباط بین گره‌ها:</p>
          <div className="flex items-center gap-1 text-light-neutral-text-low cursor-not-allowed">
            <p>مستقیم</p>
            <CaretLeft />
          </div>
        </div>

        <div
          ref={connectionRef}
          className="relative flex items-center gap-3 justify-center cursor-pointer w-52"
        >
          <p
            onClick={() => setConnectionDropdownActive((prev) => !prev)}
            className="cursor-pointer"
          >
            نوع ارتباط:
          </p>
          <div
            className="flex items-center gap-1 text-light-neutral-text-low z-10"
            onClick={() => setConnectionDropdownActive((prev) => !prev)}
          >
            <p className="select-none truncate max-w-[100px]">
              {connectionTypes.length > 0
                ? connectionTypes.join(", ")
                : "انتخاب کنید"}
            </p>
            <CaretLeft />
          </div>

          {connectionDropdownActive && (
            <div className="absolute top-full left-0 mt-2 bg-light-neutral-surface-card rounded-lg p-2 flex flex-col gap-2 font-button-medium z-0 shadow-md w-40">
              {["پست", "ریپست", "توئیت", "لایک"].map((type) => (
                <div
                  key={type}
                  onClick={() => toggleConnectionType(type)}
                  className={
                    connectionTypes.includes(type)
                      ? "flex items-center justify-between px-1 py-2 rounded-lg bg-light-neutral-surface-card-highlight"
                      : "flex items-center justify-between px-1 py-2 rounded-lg"
                  }
                >
                  {type}
                  {connectionTypes.includes(type) && <Check />}
                </div>
              ))}
            </div>
          )}
        </div>

        <div
          ref={weightRef}
          className="relative flex items-center gap-3 justify-center cursor-pointer w-52"
        >
          <p
            onClick={() => setWeightDropdownActive((prev) => !prev)}
            className="cursor-pointer"
          >
            وزن یال:
          </p>
          <div
            className="flex items-center gap-1 text-light-neutral-text-low z-10"
            onClick={() => setWeightDropdownActive((prev) => !prev)}
          >
            <p className="select-none">انتخاب کنید</p>
            <CaretLeft />
          </div>

          {weightDropdownActive && (
            <div className="absolute top-full left-0 mt-2 bg-light-neutral-surface-card rounded-lg p-3 w-96 shadow-md z-0">
              <RangeSlider
                value={RangeSliderVal - 1}
                onChange={(e) => {
                  setRangeSliderVal(parseInt(e.target.value) + 1);
                }}
                values={[
                  { value: 1 },
                  { value: 2 },
                  { value: 3 },
                  { value: 4 },
                  { value: 5 },
                  { value: 6 },
                  { value: 7 },
                  { value: 8 },
                  { value: 9 },
                  { value: 10 },
                ]}
              />
            </div>
          )}
        </div>

        <div className="flex items-center gap-3 justify-center">
          <p>بازه زمانی بررسی:</p>
          <div className="flex items-center gap-1 text-light-neutral-text-low cursor-not-allowed">
            <p>یک ماه گذشته</p>
            <CaretLeft />
          </div>
        </div>

        <div
          ref={graphTypeRef}
          className="relative flex items-center gap-3 justify-center cursor-pointer w-40"
        >
          <p
            onClick={() =>
              setShowGraphType((prev) => ({ ...prev, active: !prev.active }))
            }
            className="cursor-pointer"
          >
            نوع گره:
          </p>
          <div
            className="flex items-center gap-1 text-light-neutral-text-low z-10"
            onClick={() =>
              setShowGraphType((prev) => ({ ...prev, active: !prev.active }))
            }
          >
            <p className="select-none">{showGraphType?.value}</p>
            <CaretLeft />
          </div>
          {showGraphType?.active && (
            <div className="absolute top-full left-0 mt-2 bg-light-neutral-surface-card rounded-lg p-2 flex flex-col gap-3 font-button-medium z-0 shadow-md w-36">
              {["پست", "ریپست", "توئیت", "لایک"].map((type) => (
                <div
                  key={type}
                  onClick={() =>
                    setShowGraphType((prev) => ({
                      ...prev,
                      value: type,
                      active: false,
                    }))
                  }
                  className={
                    showGraphType.value === type
                      ? "flex items-center justify-between px-1 py-2 rounded-lg bg-light-neutral-surface-highlight"
                      : "flex items-center justify-between px-1 py-2 rounded-lg"
                  }
                >
                  {type}
                  {showGraphType?.value === type && <Check />}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <div className="w-fit">
            <CButton
              className="gap-1"
              leftIcon={<ArrowCounterClockwise size={18} />}
            >
              به‌روزرسانی
            </CButton>
          </div>
          <div className="w-fit" onClick={() => printScreenHandler()}>
            <CButton
              className="gap-1"
              role="neutral"
              leftIcon={<FileArrowDown size={18} />}
            >
              خروجی گراف
            </CButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Filters;
