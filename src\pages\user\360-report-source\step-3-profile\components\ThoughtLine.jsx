import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import { useReport360Store } from "store/report360Store";
import ExportMenu from "components/ExportMenu/index.jsx";
import { useEffect, useState } from "react";
import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";
import use360requestStore from "store/360requestStore";

const ThoughtLine = () => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
  }));

  const [thoughtLines, setThoughtLines] = useState([]);
  const [activeCategory, setActiveCategory] = useState(null);
  const [loading, setLoading] = useState(false);

  // Define all possible categories with their colors
  const allCategories = [
    { title: "نامشخص", color: "rgb(216, 216, 216)" },
    { title: "خاکستری", color: "rgba(128, 128, 128, 1)" },
    { title: "خودی", color: "rgba(0, 128, 0, 1)" },
    { title: "معاند", color: "rgba(255, 105, 180, 1)" },
  ];

  // Translator function to map API response values to Persian
  const translateCategory = (category) => {
    switch (category?.toLowerCase()) {
      case "opposite":
        return "معاند";
      case "ally":
      case "supporter":
        return "خودی";
      case "neutral":
      case "gray":
        return "خاکستری";
      case "unknown":
      case null:
      case undefined:
        return "نامشخص";
      default:
        return category || "نامشخص";
    }
  };

  useEffect(() => {
    const fetchThoughtLine = async () => {
      setLoading(true);
      try {
        // Map support_state to translated category
        const translatedTitle = translateCategory(
          sourceReport.profile?.political_category?.support_state || "unknown"
        );

        // Prepare thoughtLines with all categories, marking the active one
        const formattedData = allCategories.map((category, index) => ({
          id: `thought_${index}`,
          title: category.title,
          color: category.color,
          isActive: category.title === translatedTitle,
        }));

        setThoughtLines(formattedData);
        setActiveCategory(translatedTitle);
      } catch (error) {
        console.error("Error fetching thought line:", error);
        // Fallback to all categories with "نامشخص" as active
        const formattedData = allCategories.map((category, index) => ({
          id: `thought_${index}`,
          title: category.title,
          color: category.color,
          isActive: category.title === "نامشخص",
        }));
        setThoughtLines(formattedData);
        setActiveCategory("نامشخص");
      } finally {
        setLoading(false);
      }
    };

    if (sourceReport.profile?.user_name || sourceReport.profile?.key) {
      fetchThoughtLine();
    }
  }, [date]);

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Presence",
      data: thoughtLines.map((tab) => (tab.isActive ? 1 : 0)), // 1 for active, 0 for inactive
      time: thoughtLines.map((tab) => tab.title),
    },
  ];

  const time = thoughtLines.map((tab) => tab.title);

  return (
    <>
      {sourceReport.platform === "news" ? (
        <Card className="w-full h-full">
          <div className="flex flex-col w-full h-full">
            <div className="flex items-center w-full justify-between px-3">
              <Title title="خط فکری" />
              <ExportMenu
                chartSelector=".thought-line-container"
                fileName="thought-line"
                series={series}
                time={time}
                excelHeaders={["Category", "Presence"]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
                chartTitle="خط فکری"
              />
            </div>
            {loading ? (
              <div className="flex justify-center items-center h-full">
                Loading...
              </div>
            ) : (
              <div className="thought-line-container w-full mt-6 px-4 font-overline-medium flex flex-wrap gap-4">
                {thoughtLines.length > 0 ? (
                  thoughtLines.map((tab, i) => (
                    <span
                      key={tab.id || i}
                      className={`py-1 px-3 border rounded-md transition duration-300 ${
                        tab.isActive
                          ? "hover:bg-[#EE9FAD]"
                          : "opacity-50 cursor-default"
                      }`}
                      style={{
                        background: tab.isActive
                          ? `linear-gradient(to left, ${tab.color} 0%, ${tab.color} 10%, rgba(255, 255, 255, 0) 100%)`
                          : "linear-gradient(to left, rgba(200, 200, 200, 0.5) 0%, rgba(200, 200, 200, 0.5) 10%, rgba(255, 255, 255, 0) 100%)",
                      }}
                    >
                      {tab.title}
                    </span>
                  ))
                ) : (
                  <div className="h-16 w-full flex items-center justify-center font-subtitle-medium">
                    داده ای برای نمایش وجود ندارد
                  </div>
                )}
              </div>
            )}
          </div>
        </Card>
      ) : (
        <Card className="w-full h-full flex flex-col">
          <div className="flex items-center justify-between px-3">
            <Title title="خط فکری" />
            <ExportMenu
              chartSelector=".thought-line-container"
              fileName="thought-line"
              series={series}
              time={time}
              excelHeaders={["Category", "Presence"]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
              chartTitle="خط فکری"
            />
          </div>
          <div className="flex-1 thought-line-container flex justify-center items-center w-full">
            {loading ? (
              <div className="flex justify-center items-center h-full">
                Loading...
              </div>
            ) : (
              <div className="w-full pt-3 max-w-md px-4 font-overline-medium flex flex-col items-center justify-center gap-4">
                {thoughtLines.length > 0 ? (
                  thoughtLines.map((tab, i) => (
                    <span
                      key={tab.id || i}
                      className={`py-1 px-3 text-center border rounded-md transition duration-300 w-[6rem] ${
                        tab.isActive
                          ? "hover:bg-[#EE9FAD]"
                          : "opacity-50 cursor-default"
                      }`}
                      style={{
                        background: tab.isActive
                          ? `linear-gradient(to left, ${tab.color} 0%, ${tab.color} 10%, rgba(255, 255, 255, 0) 100%)`
                          : "linear-gradient(to left, rgba(200, 200, 200, 0.5) 0%, rgba(200, 200, 200, 0.5) 10%, rgba(255, 255, 255, 0) 100%)",
                      }}
                    >
                      {tab.title}
                    </span>
                  ))
                ) : (
                  <div className="h-72 flex items-center justify-center font-subtitle-medium">
                    داده ای برای نمایش وجود ندارد
                  </div>
                )}
              </div>
            )}
          </div>
        </Card>
      )}
    </>
  );
};

export default ThoughtLine;
