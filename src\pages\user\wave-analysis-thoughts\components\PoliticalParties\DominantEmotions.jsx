import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";

const DominantEmotions = ({
  colorMap,
  emotionMap,
  emotionsData,
  emotionsTransformedData,
  activeGroup,
}) => {
  const chartData = emotionsTransformedData?.data?.[activeGroup]?.map(
    (item) => ({
      name: emotionMap?.[item.key] || item.key,
      y: (item.count / emotionsData?.total) * 100,
      color: colorMap?.[item.key] || "#999999",
    })
  );
  const options = {
    chart: {
      type: "pie",
      margin: [0, 0, 0, 0],
      padding: [0, 0, 0, 0],
      height: 250,
      width: 350,
    },
    title: {
      text: null,
    },
    credits: {
      enabled: false,
    },
    tooltip: {
      useHTML: true,
      formatter: function () {
        return `<div dir="rtl" style='direction:rtl; font-family:iranyekan;'>
          <b>${this.key}</b>
          <p>${toPersianNumber(this.percentage.toFixed(0))}٪</p>
        </div>`;
      },
    },
    subtitle: {
      text: null,
    },
    plotOptions: {
      series: {
        size: "55%",
        allowPointSelect: true,
        cursor: "pointer",
        dataLabels: [
          {
            enabled: true,
            distance: 15,
            format: "{point.name}",
          },
          {
            enabled: false,
            distance: -40,
            format: "{point.percentage:.1f}%",
            style: {
              fontSize: "1.2em",
              textOutline: "none",
              opacity: 0.7,
            },
            filter: {
              operator: ">",
              property: "percentage",
              value: 10,
            },
          },
        ],
      },
    },
    series: [
      {
        name: "Percentage",
        colorByPoint: true,
        data: chartData,
      },
    ],
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default DominantEmotions;
