import { useState, useEffect } from "react";
import Divider from "components/ui/Divider";
import Doughnut from "components/Charts/Doughnut";
import { sentiment } from "utils/selectIcon";
import { SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import PropTypes from "prop-types";
import "../../style.css";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import { useReport360Store } from "store/report360Store";
import { formatShortNumber, toPersianNumber } from "utils/helper";
import use360requestStore from "store/360requestStore";
import usePlatformDataStore from "store/person360platform";
import ExportMenu from "components/ExportMenu/index.jsx";

const PersonSentiment = ({ activePlatform }) => {
  const report = use360requestStore((state) => state.report);
  const { profile, date, entity, type } = useReport360Store(
    (state) => state.report
  );
  const locationEntityCharts = use360requestStore(
    (state) => state.locationEntityCharts
  );
  const { platformData, setPlatformData } = usePlatformDataStore();
  const profileCleared = Array.isArray(profile.user_name)
    ? profile.user_name
    : [profile.user_name || profile.title];
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [rawCounts, setRawCounts] = useState({
    positive: 0,
    neutral: 0,
    negative: 0,
  });
  const [chartData, setChartData] = useState({
    positive: 0,
    neutral: 0,
    negative: 0,
  });
  const [isDateChange, setIsDateChange] = useState(false); // Track date changes
  const reportType =
    type === "topic" ? "entity" : type === "location" ? "location" : null;
  const sentimentData = report?.[reportType]?.[activePlatform]?.sentiments;

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Sentiment",
      data: [rawCounts.positive, rawCounts.neutral, rawCounts.negative],
      time: ["مثبت", "خنثی", "منفی"],
    },
  ];

  const time = ["مثبت", "خنثی", "منفی"];

  const fetchProfileData = async (abortController) => {
    const foo = profileCleared?.map(() => {
      const filterObject = {
        date,
        platform: activePlatform,
        q: entity,
      };
      const requestData = buildRequestData(filterObject, "sentiments");
      return requestData || [];
    });
    const reqs = foo.map((q) => advanceSearch.search(q, abortController));
    const response = await Promise.allSettled(reqs);
    return response;
  };

  const calculatePercentages = (result) => {
    const sum = result.negative + result.neutral + result.positive;
    if (sum === 0) return { negative: 0, neutral: 0, positive: 0 };
    return {
      negative: ((result.negative / sum) * 100).toFixed(2),
      neutral: ((result.neutral / sum) * 100).toFixed(2),
      positive: ((result.positive / sum) * 100).toFixed(2),
    };
  };

  const processResult = (item) => {
    let result = [
      { key: "neutral", count: 0 },
      { key: "positive", count: 0 },
      { key: "negative", count: 0 },
    ];

    item?.forEach(({ key, count }) => {
      if (key === "negative") {
        result[2].count = count || 0;
      } else if (key === "neutral") {
        result[0].count = count || 0;
      } else if (key === "positive") {
        result[1].count = count || 0;
      }
    });

    return result;
  };

  const getData = async (abortController) => {
    // Check cache first, but ignore cache if date has changed
    const cachedData = platformData[activePlatform]?.sentiment;
    if (cachedData && !isUpdate && !isDateChange) {
      setTotal(cachedData.total);
      setRawCounts(cachedData.counts);
      setChartData(calculatePercentages(cachedData.counts));
      setLoading(false);
      return;
    }

    // Use sentimentData if isUpdate is true and data exists, but ignore if date has changed
    if (isUpdate && sentimentData?.length > 0 && !isDateChange) {
      const parsedResult = processResult(sentimentData);
      const sentimentCounts = {
        negative: parsedResult[2]?.count || 0,
        neutral: parsedResult[0]?.count || 0,
        positive: parsedResult[1]?.count || 0,
      };
      const percentages = calculatePercentages(sentimentCounts);
      const total =
        sentimentCounts.negative +
        sentimentCounts.neutral +
        sentimentCounts.positive;

      setTotal(total);
      setRawCounts(sentimentCounts);
      setChartData(percentages);

      setPlatformData(activePlatform, "sentiment", {
        total,
        counts: sentimentCounts,
      });
      setLoading(false);
      return;
    }

    // Fetch new data if no cache, update required, or date has changed
    setLoading(true);
    try {
      const result = await fetchProfileData(abortController);
      if (!abortController.signal.aborted) {
        const apiData = result[0]?.value?.data?.data || {};
        const total = apiData.total || 0;
        const sentimentArray = apiData[activePlatform] || [];

        const parsedResult = processResult(sentimentArray);
        const sentimentCounts = {
          negative: parsedResult[2]?.count || 0,
          neutral: parsedResult[0]?.count || 0,
          positive: parsedResult[1]?.count || 0,
        };
        const percentages = calculatePercentages(sentimentCounts);

        setTotal(total);
        setRawCounts(sentimentCounts);
        setChartData(percentages);

        setPlatformData(activePlatform, "sentiment", {
          total,
          counts: sentimentCounts,
        });

        locationEntityCharts(
          parsedResult,
          "sentiments",
          activePlatform,
          reportType
        );
      }
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error("Error fetching data:", error);
      }
      setTotal(0);
      setRawCounts({ positive: 0, neutral: 0, negative: 0 });
      setChartData({ positive: 0, neutral: 0, negative: 0 });
    } finally {
      if (!abortController.signal.aborted) {
        setLoading(false);
        setIsDateChange(false); // Reset date change flag after fetching
      }
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    getData(abortController);
    return () => abortController.abort();
  }, [entity, date, activePlatform, isUpdate]);

  // Detect date changes and set isDateChange to true
  useEffect(() => {
    setIsDateChange(true);
  }, [date]);

  return (
    <Card className="flex flex-col gap-4 card-animation card-delay h-full">
      <div className="flex items-center justify-between">
        <p className="font-subtitle-large text-right">تحلیل احساسات</p>
        <ExportMenu
          chartSelector=".person-sentiment-container"
          fileName="person-sentiment"
          series={series}
          time={time}
          excelHeaders={["Sentiment", "Count"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
          chartTitle="تحلیل احساسات"
        />
      </div>
      <Divider />
      {loading ? (
        <div className="w-full h-[240px] flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : total > 0 ? (
        <div className="flex person-sentiment-container flex-1 justify-center items-center">
          <Doughnut
            name="sentiment"
            height={240}
            data={[
              {
                name: "مثبت",
                y: parseFloat(chartData.positive),
                count: rawCounts.positive,
              },
              {
                name: "خنثی",
                y: parseFloat(chartData.neutral),
                count: rawCounts.neutral,
              },
              {
                name: "منفی",
                y: parseFloat(chartData.negative),
                count: rawCounts.negative,
              },
            ]}
            legendFormatter={function () {
              return `<div dir="rtl" style="font-family: iranyekan; display: flex; gap: 30%; width: 100%; padding: 2px; align-items: center; margin-right: 2rem;">
                  <span style="font-size: 16px; justify-self: start;">
                    ${toPersianNumber(this.y?.toFixed(0))}%
                  </span>
                  <div style="display: flex; gap: 8px; align-items: center; padding-right: 1rem;">
                    <span style="color: ${this.color}; font-size: 14px;">
                      ${this.name}
                    </span> 
                    <img src=${
                      sentiment[this.name]
                    } style="width: 20px; height: 20px; margin-right: 15px;" />
                  </div>
                </div>`;
            }}
            tooltipFormatter={function () {
              return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan">
                <div>${this.key}</div>
                <div>${toPersianNumber(
                  formatShortNumber(this.point.count)
                )}</div>
              </div>`;
            }}
            colors={["#1CB0A5", "#00000080", "#E0526A"]}
          />
        </div>
      ) : (
        <div className="h-[240px] flex items-center justify-center font-subtitle-medium">
          داده‌ای برای نمایش وجود ندارد
        </div>
      )}
    </Card>
  );
};

PersonSentiment.propTypes = {
  activePlatform: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool,
};

export default PersonSentiment;
