const BaleLogoWhite = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.82301 0.823087C1.66768 0.754498 1.49302 0.734186 1.32552 0.759502C1.10945 0.792178 0.905447 0.904236 0.765031 1.07222C0.627166 1.2359 0.553082 1.44912 0.548961 1.66225C0.548474 1.68823 0.549412 1.71417 0.550349 1.7401C0.551301 1.76645 0.552253 1.79279 0.551709 1.81915L0.55161 5.07727C0.551907 5.12342 0.551739 5.16959 0.55157 5.21576C0.551237 5.30701 0.550904 5.39827 0.554162 5.48949C0.553753 5.52677 0.553281 5.56405 0.552809 5.60133C0.550336 5.79658 0.547865 5.99177 0.554456 6.18695C0.572413 6.60438 0.631287 7.02101 0.746682 7.42312C0.855992 7.81543 1.01024 8.19546 1.20845 8.55136C1.55866 9.18259 2.04124 9.73954 2.61487 10.1777C3.0422 10.5047 3.52144 10.7635 4.02904 10.942C4.03924 10.9519 4.05092 10.9603 4.06525 10.9634C4.1084 10.9758 4.15146 10.9885 4.19453 11.0012C4.30627 11.0341 4.41801 11.067 4.53134 11.0943C5.00567 11.2159 5.49796 11.2598 5.98672 11.2478C6.16388 11.244 6.33976 11.2221 6.51546 11.2004C6.54586 11.1966 6.57626 11.1928 6.60667 11.1891C7.47772 11.0574 8.31216 10.6976 9.00875 10.1588C9.59151 9.71227 10.0746 9.13893 10.4255 8.49464C10.5187 8.31282 10.6112 8.13001 10.6835 7.93867C10.7475 7.7747 10.8077 7.60887 10.8529 7.43853C10.8558 7.42814 10.8586 7.41774 10.8615 7.40734C10.9004 7.26706 10.9394 7.1263 10.9597 6.98196C10.9643 6.942 10.9724 6.90265 10.9805 6.86331C10.9873 6.83029 10.9941 6.79728 10.9988 6.76393C11.0202 6.63106 11.0313 6.49673 11.0378 6.3624C11.0611 6.0854 11.0591 5.80613 11.0303 5.52962C11.011 5.22396 10.9496 4.92252 10.8725 4.62668C10.5801 3.53161 9.91718 2.54095 9.02092 1.8478C8.40882 1.37258 7.68996 1.03396 6.93146 0.873425C6.67005 0.811214 6.40257 0.779618 6.13498 0.760189C5.86494 0.741055 5.59373 0.74969 5.32398 0.770492C5.14112 0.785064 4.96057 0.817362 4.7802 0.849627C4.76381 0.85256 4.74741 0.855493 4.73102 0.858412C4.10351 0.989113 3.49985 1.23521 2.96135 1.58316C2.66884 1.36434 2.37152 1.15141 2.06312 0.955457C1.98599 0.906394 1.9072 0.859098 1.82301 0.823087ZM1.30471 1.70189C1.30138 1.64625 1.31404 1.58463 1.35672 1.54558C1.39901 1.50819 1.46279 1.4918 1.51598 1.51437C1.55733 1.53099 1.59529 1.55457 1.63301 1.578C1.63583 1.57975 1.63865 1.58151 1.64148 1.58326C2.08176 1.86154 2.49673 2.1771 2.91062 2.49257C2.94152 2.47578 2.97275 2.45946 3.00399 2.44314C3.10289 2.39147 3.20191 2.33974 3.29124 2.27248C3.77058 1.94484 4.31674 1.7172 4.88458 1.59827C4.92022 1.59224 4.95581 1.58591 4.99141 1.57958C5.08444 1.56304 5.1775 1.54649 5.27139 1.53527C5.6947 1.4868 6.12448 1.49681 6.54495 1.56608C6.93038 1.63359 7.30914 1.74408 7.66445 1.90903C8.35818 2.22803 8.97156 2.72248 9.42548 3.33693C9.77785 3.81657 10.0373 4.36567 10.1738 4.94549C10.2106 5.10239 10.243 5.26066 10.2623 5.4207C10.2873 5.66817 10.3084 5.91701 10.294 6.16586C10.2912 6.2058 10.2888 6.24583 10.2864 6.28589C10.2769 6.44435 10.2674 6.60319 10.2306 6.75804C10.224 6.79169 10.2188 6.82561 10.2137 6.85954C10.2052 6.91555 10.1967 6.97156 10.1815 7.02631C10.1312 7.22089 10.0778 7.41508 10.0032 7.60191C9.93622 7.78766 9.8481 7.96477 9.75783 8.14002C9.5708 8.48238 9.34002 8.8005 9.07302 9.08486C8.55659 9.63131 7.9022 10.048 7.18599 10.278C6.98778 10.3444 6.78358 10.3907 6.57831 10.429C6.52985 10.4384 6.48084 10.4438 6.43182 10.4491C6.39518 10.4531 6.35854 10.4571 6.32211 10.4628C6.00997 10.5054 5.69313 10.5032 5.37992 10.4735C5.09035 10.4501 4.80343 10.394 4.52663 10.306C3.89539 10.1288 3.31136 9.79685 2.8228 9.36118C2.27693 8.87507 1.8442 8.25885 1.58898 7.57286C1.45318 7.20146 1.35917 6.81318 1.32797 6.41843C1.30354 6.19157 1.30187 5.96284 1.30727 5.7349C1.30715 5.00025 1.30717 4.26564 1.30719 3.53104C1.3072 2.98009 1.30721 2.42916 1.30717 1.87822C1.30715 1.86569 1.30718 1.85316 1.30722 1.84063C1.30736 1.79436 1.30749 1.74806 1.30471 1.70189ZM7.96932 3.31907C7.81183 3.31348 7.65257 3.33281 7.50264 3.38216C7.32317 3.44437 7.15754 3.54534 7.01879 3.67487C6.96846 3.72452 6.91857 3.7746 6.86868 3.82467C6.78802 3.90563 6.70737 3.98658 6.62492 4.0657C6.54863 4.14649 6.46934 4.22434 6.39006 4.30217C6.3538 4.33776 6.31754 4.37336 6.28158 4.40923C6.21353 4.48154 6.1427 4.5511 6.07186 4.62066C6.00573 4.6856 5.9396 4.75054 5.87574 4.81772C5.81611 4.87167 5.76032 4.9295 5.70452 4.98735C5.66391 5.02945 5.62329 5.07156 5.58117 5.11219C5.51301 5.17533 5.44825 5.2419 5.38348 5.30847C5.31779 5.37599 5.2521 5.4435 5.18288 5.50744C5.09605 5.42072 5.00926 5.33391 4.92246 5.24709C4.85741 5.18203 4.79236 5.11697 4.72729 5.05194C4.72302 5.04762 4.71875 5.04329 4.71449 5.03896C4.63239 4.95564 4.54976 4.87179 4.45117 4.80761C4.28161 4.69389 4.08516 4.6203 3.88273 4.5941C3.62457 4.56603 3.35836 4.61225 3.12541 4.72735C2.88932 4.85717 2.6864 5.05351 2.56532 5.29568C2.42785 5.56425 2.39645 5.8807 2.45709 6.17468C2.49702 6.33629 2.56581 6.49113 2.66079 6.62802C2.71928 6.71555 2.79406 6.7895 2.86869 6.8633C2.88833 6.88273 2.90797 6.90215 2.9273 6.9218L4.1827 8.1772L4.18889 8.18347C4.27005 8.26571 4.35177 8.3485 4.44813 8.41319C4.60728 8.5228 4.79127 8.59512 4.98173 8.62651C5.18671 8.66331 5.39925 8.63711 5.59657 8.57461C5.73709 8.52702 5.87407 8.46147 5.9874 8.36433C6.06728 8.29731 6.14264 8.22499 6.21407 8.14904C6.58255 7.78074 6.95091 7.41234 7.31927 7.04394C7.78813 6.57503 8.25698 6.10614 8.72605 5.63745C8.85303 5.52059 8.9696 5.39018 9.0534 5.23828C9.22482 4.93557 9.25288 4.56328 9.15731 4.23143C9.11767 4.08267 9.03868 3.94736 8.94536 3.82617C8.82604 3.6732 8.67444 3.54505 8.50223 3.45536C8.33905 3.36725 8.1534 3.328 7.96932 3.31907ZM7.37349 4.38891C7.43118 4.33034 7.48887 4.27178 7.54866 4.21534C7.62824 4.14645 7.72479 4.09091 7.83106 4.07963C7.95852 4.06354 8.09678 4.08012 8.20413 4.15577C8.32257 4.23653 8.42403 4.3583 8.44601 4.50353C8.46426 4.60842 8.46004 4.72009 8.41833 4.81919C8.38311 4.90201 8.3259 4.97344 8.26114 5.03507C8.09581 5.19719 7.93221 5.36118 7.76863 5.52514C7.69202 5.60193 7.61542 5.67871 7.53865 5.7553C7.21469 6.0789 6.89091 6.40272 6.56713 6.72654C6.36115 6.93256 6.15516 7.13857 5.94913 7.34452C5.91195 7.38196 5.87458 7.4192 5.83721 7.45643C5.76146 7.5319 5.68574 7.60735 5.61168 7.68442C5.59969 7.69538 5.58787 7.70671 5.57602 7.71807C5.52851 7.76363 5.48042 7.80974 5.41838 7.83416C5.31064 7.88057 5.18769 7.90403 5.0723 7.87469C4.97751 7.85261 4.88635 7.80875 4.8159 7.74084C4.5999 7.5249 4.38395 7.30891 4.168 7.09293C3.89824 6.82312 3.62847 6.55331 3.35865 6.2836C3.25121 6.1842 3.17987 6.04025 3.1835 5.89218C3.18105 5.76423 3.22197 5.63412 3.30645 5.53687C3.35551 5.48035 3.41419 5.43188 3.47974 5.39587C3.61054 5.33847 3.7644 5.32061 3.9004 5.37065C3.96977 5.39538 4.0361 5.43188 4.08929 5.48349C4.18778 5.58188 4.28619 5.68037 4.3846 5.77885C4.64835 6.0428 4.91211 6.30676 5.17758 6.56895C5.22325 6.52508 5.26931 6.48161 5.31537 6.43814C5.40492 6.35364 5.49448 6.26912 5.58117 6.18165C5.63898 6.11949 5.69976 6.06026 5.76054 6.00102C5.82055 5.94253 5.88057 5.88404 5.93775 5.82271C5.95038 5.80998 5.96295 5.79719 5.97552 5.78439C6.01325 5.74598 6.05098 5.70756 6.09043 5.67091C6.14261 5.62201 6.19236 5.57064 6.24212 5.51927C6.29849 5.46109 6.35485 5.4029 6.41473 5.34828C6.47579 5.2834 6.53943 5.22104 6.60307 5.15869C6.66849 5.09459 6.73391 5.03048 6.79654 4.96363C6.82518 4.93525 6.85387 4.90691 6.88257 4.87857C6.9856 4.77683 7.08863 4.67509 7.18933 4.57094C7.25235 4.51189 7.31292 4.4504 7.37349 4.38891Z"
        fill="white"
      />
    </svg>
  );
};

export default BaleLogoWhite;
