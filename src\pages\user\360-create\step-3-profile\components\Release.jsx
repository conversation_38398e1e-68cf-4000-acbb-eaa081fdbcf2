import { useEffect, useState, memo } from "react";
import { SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import ReleaseChart360 from "./ReleaseChart";
import "../../style.css";
import { useReport360Store } from "store/report360Store";
import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";
import PropTypes from "prop-types";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const ReleaseChartContainer = ({ isNews = false, isUpdate }) => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    process: state.report?.content?.report_info?.process,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const [series, setSeries] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchChartData = async () => {
    setLoading(true);

    try {
      const filters = {
        date,
        platform: sourceReport.platform,
      };

      sourceReport.platform === "news"
        ? (filters["sources"] = sourceReport.profile?.id
            ? [sourceReport.profile?.id?.toString()]
            : [])
        : (filters["sources"] = sourceReport.profile?.user_name
            ? [sourceReport.profile?.user_name]
            : sourceReport.profile?.key
            ? [sourceReport.profile?.key]
            : sourceReport.profile?.channel_id
            ? [sourceReport.profile?.channel_id]
            : []);

      const infoQuery = buildRequestData(filters, "process");
      const res = await advanceSearch.search(infoQuery);

      const result = res.data.data?.[filters.platform];

      const payload = result.map((item) => ({
        datetime: item.datetime,
        timestamp: item.timestamp,
        count: item.count,
      }));

      updateReportField("content.report_info.process", payload);

      const seriesData = payload.map((data) => data.count);
      const timeData = payload.map((data) => data.datetime);
      const timestampData = payload.map((data) => data.timestamp);

      setSeries([
        {
          name: `${sourceReport.profile.user_title}`,
          data: seriesData,
          time: timeData,
          timestamp: timestampData,
        },
      ]);
    } catch (error) {
      console.error("Error fetching profile chart data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Prepare series and time for ExportMenu
  const exportSeries = series.map((serie) => ({
    name: serie.name,
    data: serie.data,
    time: serie.time,
  }));

  const time = series.length > 0 ? series[0].time : [];

  useEffect(() => {
    console.log("Release");
    console.log(sourceReport);

    if (
      sourceReport.process &&
      Array.isArray(sourceReport.process) &&
      sourceReport.process.length &&
      sourceReport.date.from === date.from &&
      sourceReport.date.to === date.to
    ) {
      const seriesData = sourceReport?.process?.map((data) => data.count);
      const timeData = sourceReport?.process?.map((data) => data.datetime);
      const timestampData = sourceReport?.process?.map(
        (data) => data.timestamp
      );

      setSeries([
        {
          name: `${sourceReport.profile.user_title}`,
          data: seriesData,
          time: timeData,
          timestamp: timestampData,
        },
      ]);
    } else {
      fetchChartData();
      updateReportField("start_date", date.from);
      updateReportField("end_date", date.to);
    }
  }, [isUpdate, date]);

  return (
    <div className="flex !h-full">
      {loading ? (
        <Card className="flex flex-col gap-4 w-full !h-full card-animation card-delay">
          <div className="!flex w-full !h-full justify-center items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        </Card>
      ) : (
        <Card className="w-full h-full card-animation card-delay">
          <div className="w-full h-full flex flex-col">
            <div className="pb-3 flex items-center justify-between">
              <Title title="روند انتشار محتوا"></Title>
              <ExportMenu
                chartSelector=".release-chart-container"
                fileName="release-chart"
                series={exportSeries}
                time={time}
                excelHeaders={["Time", ...exportSeries.map((s) => s.name)]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
                chartTitle="روند انتشار محتوا"
              />
            </div>
            <div className="flex-grow release-chart-container">
              <ReleaseChart360 series={series} isNews={isNews} />
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

ReleaseChartContainer.propTypes = {
  isNews: PropTypes.bool,
  isUpdate: PropTypes.bool,
};

export default memo(ReleaseChartContainer);
