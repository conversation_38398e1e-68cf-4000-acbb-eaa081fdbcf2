import { useEffect, useState, useMemo } from "react";
import Divider from "components/ui/Divider";
import <PERSON><PERSON><PERSON> from "components/Charts/Pie";
import { SpinnerGap } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { Card } from "components/ui/Card";
import { useReport360Store } from "store/report360Store";
import use360requestStore from "store/360requestStore";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import "../../style.css";
import useChartDataStore from "store/dataCacheStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const emotionOrder = [
  { key: "anger", label: "خشم", color: "#FF3300" },
  { key: "neutral", label: "خنثی", color: "#CCCCCC" },
  { key: "fear", label: "ترس", color: "#660066" },
  { key: "sadness", label: "اندوه", color: "#004080" },
  { key: "disgust", label: "نفرت", color: "#8B4513" },
  { key: "optimism", label: "خوش‌بینی", color: "#FFFF00" },
  { key: "trust", label: "اعتماد", color: "#87CEFA" },
  { key: "disappointment", label: "ناامیدی", color: "#708090" },
  { key: "joy", label: "شادی", color: "#47DBB6" },
  { key: "surprise", label: "شگفتی", color: "#FFA500" },
];

const Excitement = ({ isUpdate }) => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    emotion: state.report?.content?.report_info?.emotion,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const [loading, setLoading] = useState(false);
  const [localChartData, setLocalChartData] = useState({
    data: [],
    colors: [],
  });

  const series = [
    {
      name: "Emotion",
      data: localChartData.data.map(({ count }) => count),
      time: localChartData.data.map(({ key }) => key),
    },
  ];

  const time = localChartData.data.map(({ key }) => key);

  const processResult = (data, total) => {
    if (!data || !Array.isArray(data) || data.length === 0 || total === 0) {
      return { chartData: [], colors: [] };
    }

    const persianToEnglishMap = Object.fromEntries(
      emotionOrder.map(({ key, label }) => [label, key])
    );

    const emotionMap = new Map(
      data.map((item) => [
        persianToEnglishMap[item.key] || item.key,
        item.count,
      ])
    );

    const processedData = emotionOrder.map(({ key, label }) => ({
      key: label,
      count: emotionMap.get(key) || 0,
    }));

    const totalCount = processedData.reduce((acc, { count }) => acc + count, 0);

    const filteredData = processedData
      .map((item, index) => ({
        ...item,
        percentage: totalCount > 0 ? (item.count / totalCount) * 100 : 0,
        color: emotionOrder[index].color,
      }))
      .filter((item) => item.percentage > 0);

    const filteredChartData = filteredData.map(({ key, count }) => ({
      key,
      count,
    }));
    const filteredColors = filteredData.map(({ color }) => color);

    return { chartData: filteredChartData, colors: filteredColors };
  };

  const getData = async () => {
    if (loading) return;
    setLoading(true);
    try {
      const filterObject = {
        date,
        platform: sourceReport.platform,
        sources:
          sourceReport.platform === "twitter"
            ? [sourceReport.profile.id.toString()] || []
            : sourceReport.profile?.channel_id
            ? [sourceReport.profile?.channel_id.toString()]
            : [],
      };

      const requestData = buildRequestData(filterObject, "emotion");
      const result = await advanceSearch.search(requestData);

      const totalData = result.data?.data?.total || 0;
      const chartData = result.data?.data?.[sourceReport?.platform] || [];

      updateReportField("content.report_info.emotion", {
        chartData: chartData,
        total: totalData,
      });

      const { chartData: convertedData, colors: filteredColors } =
        processResult(chartData, totalData);
      setLocalChartData({ data: convertedData, colors: filteredColors });
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log("emotion");
    console.log(sourceReport);

    if (
      sourceReport.emotion &&
      typeof sourceReport.emotion === "object" &&
      Object.keys(sourceReport.emotion).length &&
      sourceReport.date.from === date.from &&
      sourceReport.date.to === date.to
    ) {
      const { chartData: convertedData, colors: filteredColors } =
        processResult(
          sourceReport.emotion.chartData,
          sourceReport.emotion.total
        );
      setLocalChartData({ data: convertedData, colors: filteredColors });
    } else {
      getData();
      updateReportField("start_date", date.from);
      updateReportField("end_date", date.to);
    }
  }, [isUpdate, date]);

  // useEffect(() => {
  //   if (isUpdate) {
  //     const platformData =
  //       sourceReport.source.platform[platform]?.resource_overview.emotion;

  //     if (!platformData || !Array.isArray(platformData)) {
  //       return;
  //     }

  //     const totalCount = platformData.reduce(
  //       (acc, curr) => acc + curr.count,
  //       0
  //     );
  //     const { chartData: parsedResult, colors: filteredColors } = processResult(
  //       platformData,
  //       totalCount
  //     );

  //     const currentChartData = chartData[chartKey];
  //     const newChartData = {
  //       data: parsedResult,
  //       colors: filteredColors,
  //       total: totalCount,
  //     };

  //     if (JSON.stringify(currentChartData) !== JSON.stringify(newChartData)) {
  //       setLocalChartData({ data: parsedResult, colors: filteredColors });
  //       setChartData(chartKey, newChartData);
  //     }
  //   }
  // }, [isUpdate, sourceReport, chartKey, chartData, platform]);

  return (
    <Card className="flex flex-col gap-4 card-animation card-delay !h-full w-full">
      <div className="flex h-full w-full items-center justify-between">
        <p className="font-subtitle-large text-right">تحلیل هیجان</p>
        <ExportMenu
          chartSelector=".excitement-container"
          fileName="excitement-data"
          series={series}
          time={time}
          excelHeaders={["Emotion", "Count"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
          chartTitle="تحلیل هیجان"
        />
      </div>
      <Divider />
      <div className="flex excitement-container items-center w-full h-full">
        {loading ? (
          <div className="w-full !h-full flex justify-center items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        ) : localChartData.data?.length > 0 ? (
          <PieChart
            height={240}
            data={localChartData.data?.map(({ key, count }) => ({
              name: key,
              y: count,
            }))}
            legend={true}
            colors={localChartData.colors}
          />
        ) : (
          <div className="h-80 flex items-center w-full justify-center font-subtitle-medium">
            داده ای برای نمایش وجود ندارد
          </div>
        )}
      </div>
    </Card>
  );
};

Excitement.propTypes = {
  isUpdate: PropTypes.bool,
};

export default Excitement;
