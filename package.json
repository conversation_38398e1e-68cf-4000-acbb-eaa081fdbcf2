{"name": "stinas-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "local": "vite --port 3030", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "host": "vite --host", "docker-dev-up": "docker-compose -f docker-compose.dev.yml up", "docker-dev-down": "docker-compose -f docker-compose.dev.yml down"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^43.3.1", "@ckeditor/ckeditor5-react": "^9.4.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@headlessui/react": "^2.2.3", "@lexical/react": "^0.21.0", "@phosphor-icons/react": "^2.0.15", "@reduxjs/toolkit": "^2.0.1", "axios": "1.7.4", "chart.js": "^4.4.1", "ckeditor4-react": "^5.2.0", "clsx": "^2.1.1", "d3-dsv": "^3.0.1", "d3-force-3d": "^3.0.5", "dat.gui": "^0.7.9", "dom-to-image": "^2.6.0", "dompurify": "^3.1.6", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "formik": "^2.4.5", "framer-motion": "^12.18.1", "highcharts": "^11.4.1", "highcharts-react-official": "^3.2.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lexical": "^0.21.0", "lodash.debounce": "^4.0.8", "multi-range-slider-react": "^2.0.7", "nanoid": "5.0.8", "prop-types": "^15.8.1", "query-string": "^9.0.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-countdown": "^2.3.5", "react-date-object": "^2.1.9", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-force-graph-2d": "^1.27.0", "react-gauge-chart": "^0.5.1", "react-leaflet": "^4.2.1", "react-multi-date-picker": "^4.5.2", "react-otp-input": "^3.1.1", "react-paginate": "^8.2.0", "react-quill": "^2.0.0", "react-redux": "^9.0.4", "react-router-dom": "^6.30.1", "react-to-print": "^3.0.2", "react-toastify": "^10.0.4", "react-use": "^17.5.0", "react-wordcloud": "^1.2.7", "swiper": "^11.0.7", "tailwind-merge": "^2.3.0", "use-react-screenshot": "^4.0.0", "xlsx": "^0.18.5", "yup": "^1.3.3", "zustand": "^4.5.4"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "5.4.6"}}