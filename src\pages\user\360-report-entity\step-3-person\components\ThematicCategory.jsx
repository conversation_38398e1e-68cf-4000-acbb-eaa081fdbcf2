import { useState, useEffect } from "react";
import MultipleBar from "./MultipleBar";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import "../../style.css";
import PropTypes from "prop-types";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import { useReport360Store } from "store/report360Store";
import { SUBJECT_CATEGORIES } from "constants/subject-category.js";
import use360requestStore from "store/360requestStore";
import usePlatformDataStore from "store/person360platform";
import ExportMenu from "components/ExportMenu/index.jsx";
import { SpinnerGap } from "@phosphor-icons/react";

const PersonThematicCategory = ({ activePlatform, isUpdate }) => {
  const { date, entity, type } = useReport360Store((state) => state.report);
  const locationEntityCharts = use360requestStore(
    (state) => state.locationEntityCharts
  );
  const report = use360requestStore((state) => state.report);
  const reportType =
    type === "topic" ? "entity" : type === "location" ? "location" : null;
  const chartData = report?.[reportType]?.[activePlatform]?.categories;

  const { platformData, setPlatformData } = usePlatformDataStore();
  const platform = activePlatform;
  const [categories, setCategories] = useState([]);
  const [data, setData] = useState([]);
  const [isDateChange, setIsDateChange] = useState(false);
  const [loading, setLoading] = useState(false);

  // Prepare series and time for ExportMenu
  const series = data.map((serie) => ({
    name: serie.name,
    data: serie.data,
    time: categories,
  }));

  const time = categories;

  const getData = async (abortController) => {
    // Check if data is already cached for the current platform
    const cachedData = platformData[platform]?.thematicCategory;
    if (cachedData && !isUpdate && !isDateChange) {
      setCategories(
        cachedData.transformedData.map(
          (item) =>
            SUBJECT_CATEGORIES.find((category) => category.value === item.key)
              ?.label
        )
      );
      setData(cachedData.seriesData);
      setLoading(false);
      return;
    }

    // If no cached data or update/date change, fetch new data
    if (isUpdate === true && isDateChange === false && chartData?.length > 0) {
      const transformedData = chartData.map((item) => ({
        key: item.key,
        count: item.count,
      }));

      const categories = transformedData.map(
        (item) =>
          SUBJECT_CATEGORIES.find((category) => category.value === item.key)
            ?.label
      );

      const seriesData = [
        {
          name: "Count",
          data: transformedData.map((item) =>
            parseFloat(
              (
                (item.count /
                  chartData.reduce((sum, item) => sum + item.count, 0)) *
                100
              ).toFixed(2)
            )
          ),
          color: "#7d6cd5",
        },
      ];

      setPlatformData(platform, "thematicCategory", {
        transformedData,
        seriesData,
      });

      setCategories(categories);
      setData(seriesData);
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      const filters = {
        date,
        q: entity,
        platform,
      };

      const request = buildRequestData(filters, "categories");
      const response = await advanceSearch.search(request, abortController);

      const data = response?.data?.data || [];

      const transformedData = data[platform].map((item) => ({
        key: item.key,
        count: item.count,
      }));

      locationEntityCharts(
        transformedData,
        "categories",
        activePlatform,
        reportType
      );

      const categories = transformedData.map(
        (item) =>
          SUBJECT_CATEGORIES.find((category) => category.value === item.key)
            ?.label
      );

      const seriesData = [
        {
          name: "Count",
          data: transformedData.map((item) =>
            parseFloat(((item.count / data.total) * 100).toFixed(2))
          ),
          color: "#7d6cd5",
        },
      ];

      // Cache the fetched data
      setPlatformData(platform, "thematicCategory", {
        transformedData,
        seriesData,
      });

      setCategories(categories);
      setData(seriesData);
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error("Error fetching data:", error);
      }
      setCategories([]);
      setData([]);
    } finally {
      if (!abortController.signal.aborted) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    getData(abortController);
    return () => {
      abortController.abort();
    };
  }, [date, entity, activePlatform, isUpdate]);

  useEffect(() => {
    setIsDateChange(true);
  }, [date]);

  return (
    <div className="flex w-full">
      <Card className="!p-6 card-animation card-delay">
        <div className="flex flex-col gap-6 w-full">
          <div className="flex items-center justify-between">
            <Title title="دسته‌بندی موضوعی" />
            <ExportMenu
              chartSelector=".person-thematic-category-container"
              fileName="person-thematic-category"
              series={series}
              time={time}
              excelHeaders={["Category", "Percentage"]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
              chartTitle="دسته‌بندی موضوعی"
            />
          </div>
          <div className="person-thematic-category-container">
            {loading ? (
              <div className="w-full h-[430px] flex justify-center items-center">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            ) : data?.length && data.some((item) => item.data?.length) ? (
              <MultipleBar
                categories={categories}
                data={data}
                groupPadding={0.3}
              />
            ) : (
              <div className="h-[430px] flex items-center justify-center font-subtitle-medium">
                داده‌ای برای نمایش وجود ندارد
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

PersonThematicCategory.propTypes = {
  activePlatform: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool,
};

export default PersonThematicCategory;
