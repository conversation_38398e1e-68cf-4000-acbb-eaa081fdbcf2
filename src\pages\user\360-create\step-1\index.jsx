import { useNavigate } from "react-router-dom";
import { Card } from "components/ui/Card.jsx";
import { CButton } from "components/ui/CButton.jsx";
import { useReport360Store } from "store/report360Store.js";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import SelectType from "./components/SelectType.jsx";

const Report360CreateStep1 = () => {
  const { type } = useReport360Store((state) => state.report);
  const setReport = useReport360Store((state) => state.setReport);
  const navigate = useNavigate();

  useBreadcrumb([
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    { title: "گزارش جدید" },
  ]);

  const handleType = (type) => {
    setReport({ type });
  };

  const nextStep = () => {
    navigate("/app/report-360/create/step-2");
  };

  return (
    <div className="flex flex-col items-center justify-center pt-12">
      <div className="flex flex-col gap-4 w-fit">
        <Card>
          <div className="flex flex-col gap-6 p-6">
            <SelectType handleChange={handleType} nextStep={nextStep} />
            <div className="flex gap-2 items-center justify-center">
              <div className={"w-1/3"}>
                <CButton
                  role="neutral"
                  onClick={() => navigate("/app/report-360/list")}
                >
                  انصراف
                </CButton>
              </div>
              <div className={"w-1/3"}>
                <CButton readOnly={!type} onClick={nextStep}>
                  ادامه
                </CButton>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Report360CreateStep1;
