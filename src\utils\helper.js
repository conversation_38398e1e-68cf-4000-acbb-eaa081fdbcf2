import queryString from "query-string";
import { toast } from "react-toastify";

export const convertToEinglihsNumbers = (number) => {
  const persianNumbers = [
      /۰/g,
      /۱/g,
      /۲/g,
      /۳/g,
      /۴/g,
      /۵/g,
      /۶/g,
      /۷/g,
      /۸/g,
      /۹/g,
    ],
    arabicNumbers = [
      /٠/g,
      /١/g,
      /٢/g,
      /٣/g,
      /٤/g,
      /٥/g,
      /٦/g,
      /٧/g,
      /٨/g,
      /٩/g,
    ];

  if (typeof number === "string") {
    for (let i = 0; i < 10; i++) {
      number = number
        .replace(persianNumbers[i], i)
        .replace(arabicNumbers[i], i);
    }
  }
  return number;
};

export const parseTimeToPersian = (data) => {
  // const [date, time] = data.includes("T")
  //   ? data.slice(0, data.indexOf("+")).split("T")
  //   : data.split(" ");
  // const [year, month, day] = date.split("-");
  // const [hour, minute, second] = time.split(":");
  return new Date(data)
    .toLocaleDateString("fa-IR", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
    .split(", ")
    .reverse()
    .join(" - ")
    .replaceAll("/", "٫");
};

export const parseTimeToPersianSummary = (data) => {
  return new Date(data)
    .toLocaleDateString("fa-IR", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    })
    .replaceAll("/", "٫");
};

export const fixPercent = (percent, point = 2) => {
  if (!percent) return "0";
  return (percent * 100).toFixed(point);
};

export const fixPercentToShow = (percent, point = 0) => {
  return toPersianNumber(fixPercent(percent, point)) + "٪";
};

export const toPersianNumber = (number) => {
  const persianDigits = "۰۱۲۳۴۵۶۷۸۹";
  return number?.toString().replace(/\d/g, (d) => persianDigits[d]);
};

export const queryHandler = (Query, q, page, sort_type, sort) => {
  const [base, query] = Query.split("?");
  const parsedQuerytoObject = queryString.parse(query);
  if (page) parsedQuerytoObject.page = page;
  if (sort_type) parsedQuerytoObject.sort_type = sort_type;
  if (sort) parsedQuerytoObject.sort = sort;
  const parsedQueryToString = queryString
    .stringify(parsedQuerytoObject, { encode: false })
    .replaceAll("%2F", "/");
  return base + "?" + parsedQueryToString + q;
};

export const notification = {
  success: (text, icon) =>
    toast.success(text, {
      autoClose: 3000,
      position: "top-left",
      rtl: true,
      icon: () => icon,
      className: "font-body-medium",
      progressClassName:
        "!text-light-success-text-rest !bg-light-success-background-rest h-2",
    }),
  info: (text, icon) =>
    toast.info(text, {
      autoClose: 3000,
      position: "top-left",
      rtl: true,
      icon: () => icon,
      className: "font-body-medium",
      progressClassName:
        "!bg-light-inform-background-rest !text-light-inform-text-rest h-2",
    }),
  error: (text, icon) =>
    toast.error(text, {
      autoClose: 30000,
      position: "top-left",
      rtl: true,
      icon: () => icon,
      className: "font-body-medium",
    }),
  warning: (text, icon) =>
    toast.warning(text, {
      autoClose: 3000,
      position: "top-left",
      rtl: true,
      icon: () => icon,
      className: "font-body-medium",
    }),
};

export const parseNumber = (number = 0) => {
  try {
    if (typeof number !== "number") throw "error";
    const result = new Intl.NumberFormat("fa-IR").format(number);
    if (number > 999999) {
      return (
        result.split("٬")[0] + "," + result.split("٬")[1]?.slice(0, 1) + "M"
      );
    }
    if (number > 999) {
      return (
        result.split("٬")[0] + "," + result.split("٬")[1]?.slice(0, 1) + "k"
      );
    }
    return result;
  } catch (error) {
    return 0;
  }
};

export const preprocessWord = (
  word,
  addHashtag = false,
  ignorePersian = false
) => {
  // Define the Unicode range for Arabic/Persian characters
  const arabicRange = /[\u0600-\u06FF]/;
  if (addHashtag) word = `#${word}`;
  if (!ignorePersian) {
    if (arabicRange.test(word)) {
      if (word.startsWith("#")) {
        return word.slice(1) + "#";
      }
    }
  }
  return word;
};

export const deepEqual = (obj1, obj2) => {
  if (obj1 === obj2) {
    return true; // Same reference or primitive values are equal
  }

  if (
    typeof obj1 !== "object" ||
    typeof obj2 !== "object" ||
    obj1 === null ||
    obj2 === null
  ) {
    return false; // Different types or one is null
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false; // Different number of properties
  }

  for (let key of keys1) {
    if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {
      return false; // Different keys or values
    }
  }

  return true; // All properties and values match
};

export function formatShortNumber(number) {
  return new Intl.NumberFormat("en", {
    notation: "compact",
    maximumFractionDigits: 1,
  }).format(number);
}

export const convertToToman = (value) => {
  if (!value) return "۰ تومان";

  const numericValue = parseInt(value.toString().replace(/٬/g, ""), 10);
  if (isNaN(numericValue)) return "نامعتبر";

  const million = Math.floor(numericValue / 1_000_000);
  const thousand = Math.floor((numericValue % 1_000_000) / 1_000);
  const remainder = numericValue % 1_000;

  let result = "";

  if (million > 0) result += `${million} میلیون`;
  if (thousand > 0) result += `${result ? " و " : ""}${thousand} هزار`;
  if (remainder > 0) result += `${result ? " و " : ""}${remainder}`;

  return result + " تومان";
};

export const shortener = (str, charLength = 10, direction = "ltr") => {
  if (str && str.length > charLength) {
    return direction === "rtl"
      ? "..." + str.substring(0, charLength)
      : str.substring(0, charLength) + "...";
  }
  return str;
};
