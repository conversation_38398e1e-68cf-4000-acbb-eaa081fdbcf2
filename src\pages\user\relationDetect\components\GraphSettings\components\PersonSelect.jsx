import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import NodeCardSelect from "./NodeCardSelect";

const PersonSelect = ({ personNodesData, data, setData }) => {
  const handleSelect = (item) => {
    const exists = data?.some((node) => node.id === item.id);
    setData(
      exists ? data?.filter((node) => node.id !== item.id) : [...data, item]
    );
  };
  const transformedData = Object.keys(personNodesData).map((name, index) => ({
    id: "person-" + name,
    title: name,
    desc: "هویت فضای مجازی",
    twitter: personNodesData[name].twitter?.toString() || "0",
    telegram: personNodesData[name].telegram?.toString() || "0",
    instagram: personNodesData[name].instagram?.toString() || "0",
    news: personNodesData[name].news?.toString() || "0",
    isSelected: false,
  }));
  return (
    <>
      <p className="mb-2">شخص را جست‌وجو کنید</p>
      <div className="opinion-mining-swiper-container mb-6">
        <Swiper
          spaceBetween={210}
          slidesPerView={4}
          navigation
          pagination={{ clickable: true }}
          modules={[Navigation]}
          loop={true}
          breakpoints={{
            1280: { slidesPerView: 2.5, spaceBetween: 20 },
          }}
          className="swiper-container w-full px-6 kiosk-swiper"
        >
          {transformedData?.map((item) => (
            <SwiperSlide key={item.id}>
              <NodeCardSelect
                title={item?.title}
                twitter={item?.twitter}
                telegram={item?.telegram}
                instagram={item?.instagram}
                news={item?.news}
                handleSelect={() => handleSelect(item)}
                isSelected={data?.some((x) => x.id == item?.id)}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </>
  );
};

export default PersonSelect;
