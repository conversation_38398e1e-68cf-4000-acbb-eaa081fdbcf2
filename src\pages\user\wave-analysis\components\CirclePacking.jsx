import { useEffect, useRef } from "react";
import * as d3 from "d3";
import { Card } from "components/ui/Card";
import { toPersianNumber } from "utils/helper";
import ExportMenu from "components/ExportMenu/index.jsx";
import Title from "pages/user/compare-create/components/step-two/charts/Title";

const CirclePackingChart = ({
  active,
  data,
  setSelectedNode,
  containerRef,
}) => {
  const svgRef = useRef();
  const chartRef = useRef();

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Impact (%)",
      data: (data?.trends || []).map(
        (trend) => (trend.stats.impact || 0) * 100
      ),
      time: (data?.trends || []).map((trend) => trend.title),
    },
    {
      name: "Frequency",
      data: (data?.trends || []).map((trend) => trend.stats.posts || 0),
      time: (data?.trends || []).map((trend) => trend.title),
    },
  ];

  const time = (data?.trends || []).map((trend) => trend.title);

  const transformData = () => {
    const trendCategories = {
      Potential: "موج‌های بالقوه",
      Increasing: "موج‌های افزایشی",
      Decreasing: "موج‌های کاهشی",
      Frequent: "موج‌های فراگیر",
    };

    const categorized = (data?.trends || []).reduce((acc, trend) => {
      const category = trendCategories[trend.trend_type] || "سایر";
      if (!acc[category]) acc[category] = [];
      acc[category].push({
        name: trend.title,
        value: Math.sqrt(trend.stats.posts), // Apply square root transformation
        impact: trend.stats.impact || 0,
        frequency: trend.stats.posts || 0,
      });
      return acc;
    }, {});

    const children = Object.entries(categorized).map(([name, children]) => ({
      name,
      children,
    }));

    return {
      name: "ریشه",
      children:
        active === "همه موج‌ها"
          ? children
          : children.filter((child) => child.name === active),
    };
  };

  useEffect(() => {
    if (!data) return;

    const filteredData = transformData();
    const width = active === "همه موج‌ها" ? 920 : 660;
    const height = 659;

    const color = d3
      .scaleOrdinal()
      .domain([
        "موج‌های افزایشی",
        "موج‌های بالقوه",
        "موج‌های کاهشی",
        "موج‌های فراگیر",
      ])
      .range(["#F5C7C7", "#FFE082", "#B3E5FC", "#A3E4D7"]);

    const lightenColor = (hex, percent) => {
      const r = parseInt(hex.substr(1, 2), 16);
      const g = parseInt(hex.substr(3, 2), 16);
      const b = parseInt(hex.substr(5, 2), 16);
      const newR = Math.min(255, Math.floor(r + (255 - r) * percent));
      const newG = Math.min(255, Math.floor(g + (255 - g) * percent));
      const newB = Math.min(255, Math.floor(b + (255 - b) * percent));
      return `#${((1 << 24) + (newR << 16) + (newG << 8) + newB)
        .toString(16)
        .slice(1)}`;
    };

    const pack = (data) =>
      d3
        .pack()
        .size(
          active === "همه موج‌ها"
            ? [width - 50, height - 50]
            : [width - 200, height - 200]
        )
        .padding(5)(
        d3
          .hierarchy(data)
          .sum((d) => d.value)
          .sort((a, b) => b.value - a.value)
      );

    const root = pack(filteredData);
    const svg = d3
      .select(svgRef.current)
      .attr("viewBox", `-${width / 2} -${height / 2} ${width} ${height}`)
      .attr("width", width)
      .attr("height", height)
      .attr(
        "style",
        `max-width: 100%; height: auto; display: block; margin: 0 auto; background: #FFFFFF; cursor: pointer;`
      );

    svg.selectAll("*").remove();

    // Create tooltip
    const tooltip = d3
      .select(chartRef.current)
      .append("div")
      .attr("class", "tooltip")
      .style("position", "absolute")
      .style("background", "white")
      .style("border", "1px solid #ccc")
      .style("padding", "5px 10px")
      .style("border-radius", "4px")
      .style("box-shadow", "0 2px 4px rgba(0,0,0,0.2)")
      .style("font-family", "iranyekan")
      .style("font-size", "12px")
      .style("direction", "rtl")
      .style("pointer-events", "none")
      .style("opacity", 0);

    const node = svg
      .append("g")
      .selectAll("circle")
      .data(root.descendants().slice(1))
      .join("circle")
      .attr("fill", (d) => {
        if (!d.children && d.parent) {
          return lightenColor(color(d.parent.data.name), 0.2);
        } else if (d.children) {
          return color(d.data.name);
        }
        return "white";
      })
      .attr("stroke", (d) => (d.children ? "#000000" : null))
      .attr("stroke-width", 0)
      .attr("pointer-events", "all")
      .on("mouseover", function (event, d) {
        d3.select(this).attr("stroke", "#000").attr("stroke-width", 1);
        if (!d.children) {
          tooltip
            .style("opacity", 1)
            .html(
              `
              <div style="display: flex; align-items: center; gap: 4px;">
                <b>${d.data.name}</b>
              </div>
              <div style="display: flex; align-items: center; gap: 4px;">
                <p>تاثیرگذاری: ${toPersianNumber(
                  ((d.data.impact || 0) * 100).toFixed(0)
                )}%</p>
              </div>
              <div style="display: flex; align-items: center; gap: 4px;">
                <p>فراگیری: ${toPersianNumber(d.data.frequency || 0)}</p>
              </div>
            `
            )
            .style("left", `${event.pageX + 10}px`)
            .style("top", `${event.pageY - 10}px`);
        }
      })
      .on("mousemove", (event) => {
        tooltip
          .style("left", `${event.pageX + 10}px`)
          .style("top", `${event.pageY - 10}px`);
      })
      .on("mouseout", function (event, d) {
        d3.select(this)
          .attr("stroke", (d) => (d.children ? "#000000" : null))
          .attr("stroke-width", 0);
        tooltip.style("opacity", 0);
      })
      .on("click", (event, d) => {
        event.stopPropagation();
        if (!d.children && setSelectedNode) {
          setSelectedNode(d.data.name);
          zoom(event, d);
        } else {
          zoom(event, d);
        }
      });

    const label = svg
      .append("g")
      .style("font-family", "IranYekan")
      .attr("pointer-events", "none")
      .attr("text-anchor", "middle")
      .selectAll("text")
      .data(root.descendants().slice(1))
      .join("text")
      .style("font-size", (d) =>
        d.children ? "0px" : Math.min(12, (d.r * 2) / 10) + "px"
      )
      .style("fill-opacity", (d) => (d.children ? 0 : 1))
      .style("display", (d) => (d.children ? "none" : "inline"))
      .style("fill", "#000000")
      .text((d) => {
        const maxLength = d.children ? 25 : 17;
        return d.data.name.length > maxLength
          ? d.data.name.substring(0, maxLength - 3) + "..."
          : d.data.name;
      });

    svg.on("click", (event) => zoom(event, root));
    let focus = root;
    let view;
    zoomTo([focus.x, focus.y, focus.r * 2]);

    function zoomTo(v) {
      const k = width / v[2];
      view = v;
      label.attr(
        "transform",
        (d) => `translate(${(d.x - v[0]) * k},${(d.y - v[1]) * k})`
      );
      node.attr(
        "transform",
        (d) => `translate(${(d.x - v[0]) * k},${(d.y - v[1]) * k})`
      );
      node.attr("r", (d) => d.r * k);
      label.style("font-size", (d) =>
        d.children ? "0px" : Math.min(24, (d.r * k * 2) / 10) + "px"
      );
    }

    function zoom(event, d) {
      focus = d;
      const transition = svg
        .transition()
        .duration(event.altKey ? 7500 : 750)
        .tween("zoom", () => {
          const i = d3.interpolateZoom(view, [focus.x, focus.y, focus.r * 2]);
          return (t) => zoomTo(i(t));
        });

      label
        .filter(
          (d) =>
            d.parent === focus || (!d.children && d.parent?.parent === focus)
        )
        .transition(transition)
        .style("fill-opacity", (d) => (d.children ? 0 : 1))
        .style("display", (d) => (d.children ? "none" : "inline"))
        .style("font-size", (d) =>
          d.children
            ? "0px"
            : Math.min(24, (d.r * (width / (focus.r * 2)) * 2) / 10) + "px"
        );
    }

    return () => {
      d3.select(chartRef.current).select(".tooltip").remove();
    };
  }, [active, data, setSelectedNode]);

  return (
    <Card
      className="circle-packing-chart-container"
      style={{ direction: "rtl", width: "100%", textAlign: "center" }}
      ref={containerRef}
    >
      <div
        style={{ direction: "rtl", width: "100%", textAlign: "center" }}
        ref={chartRef}
      >
        <svg ref={svgRef}></svg>
      </div>
      <ExportMenu
        chartSelector=".circle-packing-chart-container"
        fileName="circle-packing-chart"
        series={series}
        time={time}
        excelHeaders={["Trend", "Impact (%)", "Frequency"]}
        onError={(error) => console.error("Export error:", error)}
        menuItems={["PNG", "JPEG", "Excel"]}
        customExcelFormat="radialCluster"
        data={data}
      />
    </Card>
  );
};

export default CirclePackingChart;
