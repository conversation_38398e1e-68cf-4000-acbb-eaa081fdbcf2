import { CaretDown, CaretUp } from "@phosphor-icons/react";
import clsx from "clsx";
import { useEffect, useState } from "react";
import { useMeasure } from "react-use";

const Accordion = ({
  children,
  Header,
  isOpen = false,
  showBox = true,
  disableToggle = false,
}) => {
  const [open, setOpen] = useState(isOpen);
  const [child, { height: child_height }] = useMeasure();
  const [header, { height: header_height }] = useMeasure();

  useEffect(() => {
    setOpen(isOpen);
  }, [isOpen]);

  return (
    <div
      className={clsx(
        "transition-all duration-75 px-6",
        showBox ? "bg-white shadow-[0px_2px_20px_0px_#0000000D] rounded-lg" : ""
      )}
      style={{
        height: open ? child_height + header_height + 16 : header_height,
      }}
    >
      <div
        ref={header}
        className={clsx("hover:cursor-pointer", {
          "pointer-events-none": disableToggle,
        })}
      >
        <div
          className="flex items-center justify-between w-full py-6"
          onClick={() => !disableToggle && setOpen((l) => !l)}
        >
          <>{Header}</>
          <>{open ? <CaretUp size={18} /> : <CaretDown size={18} />}</>
        </div>
      </div>
      <div
        ref={child}
        className="pb-6"
        style={{ display: open ? "block" : "none" }}
      >
        {children}
      </div>
    </div>
  );
};

export default Accordion;
