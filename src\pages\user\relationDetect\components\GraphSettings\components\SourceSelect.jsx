import UserCardSelect from "components/ui/UserCardSelect";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";

const SourceSelect = ({ nodesData, data, setData }) => {
  const handleSelect = (item) => {
    const exists = data?.some((node) => node.id === item.id);
    setData(
      exists ? data?.filter((node) => node.id !== item.id) : [...data, item]
    );
  };
  return (
    <>
      <p className="mb-2">منبع را انتخاب کنید</p>
      <div className="opinion-mining-swiper-container mb-6">
        <Swiper
          spaceBetween={100}
          slidesPerView={5}
          navigation
          pagination={{ clickable: true }}
          modules={[Navigation]}
          loop={true}
          className="swiper-container w-full px-6 kiosk-swiper"
        >
          {nodesData?.map((item) => (
            <SwiperSlide key={item.id}>
              <UserCardSelect
                avatar={item?.avatar}
                handleSelect={() => handleSelect(item)}
                title={item?.user_title}
                accountId={item?.user_name}
                followers={item?.follower_count}
                isSelected={data?.some((x) => x.id == item?.id)}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </>
  );
};

export default SourceSelect;
