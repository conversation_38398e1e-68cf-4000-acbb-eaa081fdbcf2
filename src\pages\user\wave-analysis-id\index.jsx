import { ChartDonut, DownloadSimple, FileText } from "@phosphor-icons/react";
import Divider from "components/ui/Divider";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { useEffect, useState } from "react";
import WaveInfo from "./wave-info";
import WaveRelatedInfo from "./WaveRelatedInfo";
import { useLocation } from "react-router-dom";
import waveAnalytics from "service/api/waveAnalytics";
import DropDown from "components/ui/DropDown";
import { CButton } from "components/ui/CButton";

const WaveAnalysisId = () => {
  const location = useLocation();
  const { title, date, elements, query_start_time, query_end_time } =
    location.state || {};
  const trendsDate = new Date(date);
  const [activeTab, setActiveTab] = useState("تحلیل موج");
  const [data, setData] = useState([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const from = new Date(
    trendsDate.getFullYear(),
    trendsDate.getMonth(),
    trendsDate.getDate()
  );
  const to = new Date(
    trendsDate.getFullYear(),
    trendsDate.getMonth(),
    trendsDate.getDate() + 1
  );
  const breadcrumbList = [
    { title: "موج شناسی", link: "/app/wave-analysis/list" },
    { title },
  ];
  useBreadcrumb(breadcrumbList);
  const tabs = [
    { title: "تحلیل موج", icon: ChartDonut },
    {
      title: "محتوای مرتبط با موج",
      icon: FileText,
    },
  ];
  const fullAnalysisHandler = async () => {
    try {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      const response = await waveAnalytics.sendWaveData({
        adv: [],
        categories: [],
        end_date: query_end_time,
        gender: [],
        hashtags: elements.map((item) => item?.content),
        keywords: [],
        languages: [],
        order: "desc",
        page: page,
        platform: "twitter",
        q: "*",
        report_type: "advance",
        rows: 12,
        sentiment: [],
        sort: "date",
        sources: [],
        start_date: query_start_time,
      });

      // const response = await waveAnalytics.sendWaveData({
      //   adv: [],
      //   categories: [],
      //   // end_date: today.toISOString(),
      //   end_date: query_end_time,
      //   gender: [],
      //   hashtags: elements.map((item) => item?.content),
      //   keywords: [],
      //   languages: [],
      //   order: "desc",
      //   page: 1,
      //   platform: "twitter",
      //   q: "*",
      //   report_type: "advance",
      //   rows: 10,
      //   sentiment: [],
      //   sort: "date",
      //   sources: [],
      //   // start_date: yesterday.toISOString(),
      //   start_date: query_start_time,
      // });
      setData(response?.data?.data?.twitter || []);
      setTotal(response?.data?.data?.total);
    } catch (error) {
      console.log("Error fetching wave data:", error);
      setData([]);
    }
  };
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const SORT_TYPE = {
    telegram: [
      { fa: "زمان انتشار", en: "date" },
      { fa: "بازدید", en: "view" },
    ],
    twitter: [
      { fa: "زمان انتشار", en: "date" },
      { fa: "ریتوئیت", en: "retweet" },
      { fa: "بازدید", en: "view" },
      { fa: "لایک", en: "like" },
      { fa: "بوکمارک", en: "bookmark" },
    ],
    instagram: [
      { fa: "زمان انتشار", en: "date" },
      { fa: "لایک", en: "like" },
      { fa: "نظرات", en: "comment" },
    ],
    news: [
      { fa: "زمان انتشار", en: "date" },
      // { fa: "تعداد نظرات", en: "comment_count" },
    ],
  };

  const [sort, setSort] = useState("");

  useEffect(() => {
    fullAnalysisHandler();
  }, [page]);

  const downloadHandler = async () => {
    // try {
    //   const res = await search.exportData({
    //     params: q,
    //     platform: platform,
    //     type: activeTab,
    //     fields: checked,
    //     filename: fileName,
    //     content_count: contentCount,
    //   });
    //   if (res?.data?.status !== "OK") {
    //     return false;
    //   }
    // } catch (error) {
    //   console.log(error);
    // } finally {
    //   // setIsSubmitting(false);
    //   // setShowDrawer(false);
    // }
  };
  return (
    <>
      <div className="grid grid-cols-12 gap-4 px-6">
        <div className="col-span-12 flex items-center justify-between gap-6 font-body-medium">
          <div className="flex items-center gap-6">
            {tabs?.map((item) => {
              const IconComponent = item.icon;
              return (
                <div key={item?.title} className="flex items-center gap-3">
                  <IconComponent size={20} />
                  <p
                    className={`cursor-pointer ${
                      item?.title === activeTab
                        ? "border-b-2 border-light-primary-text-rest text-black"
                        : "text-light-neutral-text-medium"
                    }`}
                    onClick={() => setActiveTab(item?.title)}
                  >
                    {`${item?.title}`}
                  </p>
                </div>
              );
            })}
          </div>
          {activeTab === "محتوای مرتبط با موج" && (
            <div className="flex item-center gap-x-2">
              <div className="w-fit" onClick={downloadHandler}>
                <CButton
                  className="gap-2"
                  leftIcon={<DownloadSimple size={18} />}
                >
                  دانلود
                </CButton>
              </div>
              <div className="w-fit">
                <DropDown
                  title="نمایش بر اساس"
                  subsets={SORT_TYPE["twitter"].map((item) => item.fa)}
                  selected={sort.fa}
                  setSelected={(value) => {
                    setSort(
                      SORT_TYPE["twitter"].filter(
                        (item) => item.fa === value
                      )[0]
                    );
                  }}
                  onOpenChange={(e) => {
                    setIsDropdownOpen(!isDropdownOpen);
                  }}
                />
              </div>
            </div>
          )}

          {/* <div className="w-fit">
            <ToolTip comp={"دانلود خروجی اکسل"}>
              <CButton
                mode={"outline"}
                rightIcon={<FileArrowDown size={22} />}
                onClick={() => setShowDrawer(true)}
                className={"!pl-2 !pr-1"}
              />
            </ToolTip>
          </div> */}
        </div>

        <div className="col-span-12">
          <Divider />
        </div>
      </div>

      {activeTab === "تحلیل موج" ? (
        <div>
          <WaveInfo from={from} to={to} />
        </div>
      ) : activeTab === "محتوای مرتبط با موج" ? (
        <WaveRelatedInfo
          data={data}
          setPage={setPage}
          page={page}
          total={total}
        />
      ) : (
        ""
      )}

      {/* <div className={!showDrawer ? "hidden" : ""}>
        <DownloadDrawer
          setShowDrawer={setShowDrawer}
          platform={"twitter"}
          q={title}
        />
      </div> */}
    </>
  );
};

export default WaveAnalysisId;
