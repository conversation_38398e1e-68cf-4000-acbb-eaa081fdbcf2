import { useState, useEffect, useRef } from "react";
import ReportsType from "./components/ReportsType";
import ReportsInfo from "./components/ReportsInfo";
import OverviewTab from "./overviewTab";
import ContactsTab from "./contactsTab";
import AnalysisTab from "./analysisTab";
import UserInfo from "./components/userInfo";
import SourceContent from "./source-content";
import ProfileSelector from "./components/ProfileSelector";
import { ToastContainer } from "react-toastify";
import { useReport360Store } from "store/report360Store";
import useChartDataStore from "store/dataCacheStore";
import use360requestStore from "store/360requestStore";

const Step3Profile = ({ isUpdate, setIsUpdate, isEdit }) => {
  const [activeSpan, setActiveSpan] = useState("overview");
  const { profile, date } = useReport360Store((state) => state.report);
  const title = profile?.user_title;
  const [isFixed, setIsFixed] = useState(false);
  const { clearAllChartData } = useChartDataStore();
  const updateReport = use360requestStore((state) => state.updateReportField);

  const prevDateRef = useRef(date);
  const prevTitleRef = useRef(title);

  // useEffect(() => {
  //   if (profile) {
  //     updateReport("content.source_info", profile);
  //     updateReport("content.report_platform", profile.platform);
  //   }
  // }, []);

  useEffect(() => {
    const hasDateChanged =
      prevDateRef.current?.start !== date?.start ||
      prevDateRef.current?.end !== date?.end;
    const hasTitleChanged = prevTitleRef.current !== title;

    if (hasDateChanged || hasTitleChanged) {
      clearAllChartData();
    }

    prevDateRef.current = date;
    prevTitleRef.current = title;
  }, [date, title, clearAllChartData]);

  useEffect(() => {
    const handleScroll = () => {
      const offset = 100;
      if (window.scrollY > offset) {
        setIsFixed(true);
      } else {
        setIsFixed(false);
      }
    };

    if (activeSpan !== "analysis" && activeSpan !== "source-content") {
      window.addEventListener("scroll", handleScroll);
    }

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [activeSpan]);

  return (
    <>
      <div className="px-5 w-[100%] flex flex-col">
        <ProfileSelector isEdit={isEdit} />
        <div
          className={
            activeSpan !== "source-content" && isFixed
              ? "fixed w-[85%] z-10"
              : "z-10"
          }
        >
          {activeSpan !== "source-content" && isFixed && (
            <div className="absolute top-[-20px] left-0 w-full h-[calc(90%+20px)] bg-gradient-to-t from-white via-white/90 to-transparent backdrop-blur-md" />
          )}
          <UserInfo
            isEdit={isEdit}
            isFixed={isFixed}
            activeSpan={activeSpan}
            isUpdate={isUpdate}
          />
        </div>
        <ReportsType
          activeTab={activeSpan}
          isFixed={isFixed}
          setIsFixed={setIsFixed}
          activeSpan={activeSpan}
          setActiveSpan={setActiveSpan}
        />
        {/* <ReportsInfo
          activeSpan={activeSpan}
          sort={sort}
          setSort={setSort}
          onDropdownOpenChange={handleDropdownState}
        /> */}
        <div>
          <div className={activeSpan !== "overview" ? "hidden" : "block"}>
            <OverviewTab isUpdate={isUpdate} setIsUpdate={setIsUpdate} />
          </div>
          <div className={activeSpan !== "contacts" ? "hidden" : "block"}>
            <ContactsTab isUpdate={isUpdate} setIsUpdate={setIsUpdate} />
          </div>
          <div className={activeSpan !== "analysis" ? "hidden" : "block"}>
            <AnalysisTab isUpdate={isUpdate} />
          </div>
          <div className={activeSpan !== "source-content" ? "hidden" : "block"}>
            <SourceContent isUpdate={isUpdate} />
          </div>
          {/* {activeSpan === "contacts" && <ContactsTab />}
          {activeSpan === "analysis" && <AnalysisTab isUpdate={isUpdate} />}
          {activeSpan === "source-content" && (
            <SourceContent
              isUpdate={isUpdate}
              sort={sort}
              setSort={setSort}
              setIsDropdownOpen={setIsDropdownOpen}
              isDropdownOpen={isDropdownOpen}
            />
          )} */}
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

export default Step3Profile;
