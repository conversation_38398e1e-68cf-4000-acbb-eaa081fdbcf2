import {
  CaretDown,
  CheckCircle,
  MagnifyingGlass,
  Plus,
  SpinnerGap,
  StarFour,
} from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { useState, useEffect } from "react"; // Added useEffect
import GraphContent from "./GraphContent";
import Drawer from "components/Drawer";
import NodeTypeCard from "../GraphSettings/components/NodeTypeCard";
import { CInput } from "components/ui/CInput";
import { useRelationStore } from "store/relationDetectStore";
import { buildRequestData } from "utils/requestData";
import nodeTypeData from "../GraphSettings/constants/nodeType";
import SourceSelect from "./SourceSelect";
import PersonSelect from "./PersonSelect";
import OrgSelect from "./OrgSelect";
import PlaceSelect from "./PlaceSelect";
import { notification } from "utils/helper";
import { useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import Filters from "./Filters";
import advanceSearch from "service/api/advanceSearch";
import relationDetect from "service/api/relationDetect";

const GraphPreview = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [entityVal, setEntityVal] = useState("");
  const [selectedNode, setSelectedNode] = useState(1);
  const [date, setDate] = useState({
    from: new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000),
    to: new Date(),
  });
  const [data, setData] = useState([]);
  const [nodesData, setNodesData] = useState([]);
  const [personNodesData, setPersonNodesData] = useState([]);
  const [organNodesData, setOrganNodesData] = useState([]);
  const [placeNodesData, setPlaceNodesData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [finalGraphData, setFinalGraphData] = useState([]);

  const nodeDetails = useRelationStore((state) => state.relation.nodeDetails);
  const setNodeDetailsBulk = useRelationStore(
    (state) => state?.setNodeDetailsBulk
  );
  const setType = useRelationStore((state) => state?.setType);
  const edgeDetails = useRelationStore((state) => state.relation.edgeDetails);

  // Trigger generateGraphRelation when nodeDetails changes
  useEffect(() => {
    if (nodeDetails.length > 0) {
      generateGraphRelation();
    }
  }, [nodeDetails]);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  const edgeTypeRenderer = {
    1: <SourceSelect nodesData={nodesData} data={data} setData={setData} />,
    2: (
      <PersonSelect
        personNodesData={personNodesData}
        data={data}
        setData={setData}
      />
    ),
    3: (
      <OrgSelect
        organNodesData={organNodesData}
        data={data}
        setData={setData}
      />
    ),
    4: (
      <PlaceSelect
        placeNodesData={placeNodesData}
        data={data}
        setData={setData}
      />
    ),
  };

  const clearSearchData = () => {
    setNodesData([]);
    setPersonNodesData([]);
    setOrganNodesData([]);
    setPlaceNodesData([]);
  };

  const search = async () => {
    if (entityVal.length < 2) {
      return;
    }
    setLoading(true);
    clearSearchData();
    try {
      const filterObject = {
        platform: selectedNode === 1 ? "twitter" : "all",
        q: entityVal,
        page: 1,
        rows: 10,
        date: {
          from: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
          to: new Date(Date.now()), // Now
        },
      };
      const req = buildRequestData(
        filterObject,
        selectedNode === 1 ? "search_in_source" : "similar_phrases"
      );
      const responseStinas = await advanceSearch?.search(
        req,
        false,
        selectedNode === 2
          ? { cloud_type: "person" }
          : selectedNode === 3
          ? { cloud_type: "organ" }
          : selectedNode === 4
          ? { cloud_type: "location" }
          : ""
      );
      const parsedResponse = responseStinas?.data?.data?.["twitter"];
      selectedNode === 1
        ? setNodesData(parsedResponse)
        : selectedNode === 2
        ? setPersonNodesData(responseStinas?.data?.data?.phrases)
        : selectedNode === 3
        ? setOrganNodesData(responseStinas?.data?.data?.phrases)
        : setPlaceNodesData(responseStinas?.data?.data?.phrases);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const searchNodeHandler = () => search();

  function separateData(data) {
    const person = [];
    const source = [];
    const organization = [];
    const location = [];

    data.forEach((item) => {
      const entry = item.title || item.user_name || "";

      if (item.id.startsWith("person-")) {
        person.push(entry);
      } else if (item.id.startsWith("organization-")) {
        organization.push(entry);
      } else if (item.id.startsWith("location-")) {
        location.push(entry);
      } else {
        source.push(entry);
      }
    });

    return { person, source, organization, location };
  }

  const generateGraphRelation = async () => {
    try {
      const separatedData = separateData(nodeDetails);
      const res = await relationDetect?.generateGraph({
        relation: separatedData,
        connection_mode: edgeDetails?.relationType,
        relation_type: edgeDetails?.relationIndex,
        node_weight: edgeDetails?.nodeWeight,
        start_date: "2025-06-10T06:51:45.285Z",
        end_date: "2025-06-17T06:51:45.285Z",
      });
      setFinalGraphData(res?.data?.data);
      // navigate("/app/relation-detect/results", {
      //   state: {
      //     data: res?.data?.data,
      //   },
      // });
    } catch (error) {
      notification.error(
        "حد اقل دو گره را انتخاب کنید",
        <CheckCircle size={20} className="text-light-error-background-rest" />
      );
    }
  };
  const printScreenHandler = () => {};
  return (
    <>
      <Filters printScreenHandler={printScreenHandler} />

      <div className="px-4 mt-4">
        <div
          className="bg-light-neutral-surface-card px-4 pt-4 rounded-lg mb-4 flex justify-between"
          onClick={toggleAccordion}
        >
          <div>
            <div className="cursor-pointer">
              <div className="flex items-center flex-row-reverse justify-end gap-2">
                <p className="font-subtitle-medium text-[14px]">
                  تحلیل منبع از نگاه هوش مصنوعی
                </p>
                <StarFour size={20} />
              </div>
              <p className="font-body-small text-light-neutral-text-low mb-6">
                این محتوا توسط هوش مصنوعی بر اساس فعالیت‌های این اکانت نوشته شده
                است
              </p>
            </div>
            <div
              className={`transition-all duration-300 ease-in-out overflow-hidden ${
                isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
              }`}
            >
              <p className="font-paragraph-large pb-4">
                لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ، و با
                استفاده از طراحان گرافیک است، چاپگرها و متون بلکه روزنامه و مجله
                در ستون و سطرآنچنان که لازم است، و برای شرایط فعلی تکنولوژی مورد
                نیاز، و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد،{" "}
              </p>
            </div>
          </div>
          <CaretDown size={20} className="cursor-pointer" />
        </div>

        <div
          className="bg-light-neutral-surface-card rounded-lg p-3 h-full font-button-medium"
          style={{
            boxShadow: "0px 2px 20px 0px #0000000D",
          }}
        >
          <div className="flex items-center justify-between">
            <p className="font-subtitle-large">گراف ارتباط سازی</p>
            <div
              className="w-fit"
              onClick={() => setIsSidebarOpen((prev) => !prev)}
            >
              <CButton
                className="gap-1"
                leftIcon={<Plus size={18} />}
                mode="outline"
              >
                افزودن گره جدید
              </CButton>
            </div>
          </div>

          <GraphContent finalGraphData={finalGraphData} />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-7 font-body-medium">
              <div className="flex items-center gap-2">
                <div className="border border-[#1DCEA3] w-6"></div>
                <p>ریپلای</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="border border-[#DB6DE5] w-6"></div>
                <p>کوت</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="border border-[#FCB353] w-6"></div>
                <p>ریتوئیت</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="border border-[#6D72E5] w-6"></div>
                <p>منشن</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="border border-black w-6"></div>
                <p>بیش از یک نوع ارتباط</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {isSidebarOpen && (
        <Drawer setShowMore={setIsSidebarOpen}>
          <div className="">
            <p className="font-subtitle-large">افزودن گره جدید</p>
            <div className="font-body-large">
              <p className="mb-2 pt-6">نوع گره را انتخاب کنید</p>
              <div className="flex items-center justify-around gap-2 my-4">
                {nodeTypeData?.map((item) => (
                  <div
                    key={item.id}
                    onClick={() => {
                      setSelectedNode(item.id);
                      setType(item?.type);
                    }}
                  >
                    <NodeTypeCard
                      classname="!w-[112px] !h-[112px]"
                      img={item.img}
                      title={item?.type === "source" ? "منبع" : item.title}
                      isSelected={selectedNode === item.id}
                    />
                  </div>
                ))}
              </div>
              <div className="mt-10 mb-3">
                <p className="-mb-2">موجودیت را جست‌وجو کنید</p>
                <div className="flex items-center gap-2">
                  <CInput
                    value={entityVal}
                    onChange={(e) => setEntityVal(e?.target?.value)}
                    headingIcon={<MagnifyingGlass />}
                    placeholder="نام موجودیت مورد نظر را برای جست‌وجو وارد کنید"
                    className="w-full pt-5"
                  />
                  <div className="w-20">
                    <CButton
                      readOnly={!entityVal.trim() || loading}
                      onClick={searchNodeHandler}
                    >
                      جست‌وجو
                    </CButton>
                  </div>
                </div>
              </div>
              {loading ? (
                <div className="flex justify-center mb-10">
                  <SpinnerGap size={26} className="animate-spin" />
                </div>
              ) : (
                entityVal.trim() && edgeTypeRenderer[selectedNode]
              )}
              <CButton
                leftIcon={<Plus size={18} className="ml-2" />}
                readOnly={data.length < 1}
                onClick={() => {
                  setNodeDetailsBulk(data);
                  setIsSidebarOpen(false); // Close the drawer
                }}
              >
                افزودن گره
              </CButton>
            </div>
          </div>
        </Drawer>
      )}
      <ToastContainer />
    </>
  );
};

export default GraphPreview;
