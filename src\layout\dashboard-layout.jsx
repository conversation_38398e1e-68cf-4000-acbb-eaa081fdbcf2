import { Outlet } from "react-router-dom";
// Components
import { Header } from "../components/Header";
import { Sidebar } from "../components/Sidebar";
import { useLayoutContext } from "../context/layout-context.jsx";
// Assets
import bg from "../assets/images/pattern.png";
import Footer from "./Footer.jsx";
function DashboardLayout() {
  const { isSidebarOpened } = useLayoutContext();
  return (
    <div
      className={
        "flex h-full w-full bg-light-neutral-surface-zero min-w-[1220px]"
      }
    >
      <div className="sticky top-0 right-0 z-[100]">
        <Sidebar />
      </div>
      <div
        className={"flex flex-col flex-auto"}
        style={{
          maxWidth: isSidebarOpened
            ? "calc(100% - 225px)"
            : "calc(100% - 65px)",
        }}
      >
        <div className="sticky top-0 right-0 z-[100]">
          <Header />
        </div>
        <div style={{ minHeight: "calc(100vh - 116px)" }}>
          <div
            className={
              "fixed w-full h-full top-0 right-0 bg-no-repeat bg-center bg-cover z-0"
            }
            style={{ backgroundImage: `url(${bg})` }}
          ></div>
          <div className="relative z-1 pt-4 pb-6">
            <Outlet />
          </div>
        </div>
        <Footer />
      </div>
    </div>
  );
}

export default DashboardLayout;
