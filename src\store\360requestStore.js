import { create } from "zustand";

const initialState = {
  report: {
    report_type: "source",
    start_date: "",
    q: "",
    end_date: "",
    title: "test",
    description: "",
    content: {
      source_info: {},
      report_info: {},
      report_platform: "",
    },
    source: {
      report_platform: "",
      source_info: {
        source_name: "",
        source_id: "",
      },
      platform: {
        telegram: { resource_overview: {}, analysis_thoughts: {} },
        twitter: {
          resource_overview: {
            description: "",
            process: [],
            content_type_distribution: [],
            sentiments: [],
            emotion: [],
            frequent_words: [],
            frequent_hashtags: [],
            frequent_entities: {
              organ: [],
              person: [],
              location: [],
              event: [],
            },
            categories: [],
            identity: {
              ai_summary: null,
              avatar: null,
              location: null,
              join_date: null,
              tweet_count: 0,
              follower_count: 0,
              following_count: 0,
              original_avatar: null,
            },
          },
          analysis_thoughts: {
            political_category: {},
          },
        },
        instagram: { resource_overview: {}, analysis_thoughts: {} },
        news: {
          resource_overview: {
            process: [],
            frequent_words: [],
            frequent_entities: {
              organ: [],
              person: [],
              location: [],
              event: [],
            },
            categories: [],
          },
          analysis_thoughts: {},
        },
      },
    },
    entity: {
      overview: {
        description: "",
        badges: {
          instagram: [],
          telegram: [],
          twitter: [
            {
              source: [],
              post: [],
            },
          ],
          eitaa: [],
          news: [
            {
              source: [],
              post: [],
            },
          ],
        },
        instagram_process: [],
        telegram_process: [],
        twitter_process: [],
        news_process: [],
        frequent_entities: {},
      },
      telegram: {
        statistical: [],
        process: [],
        categories: [],
        frequent_words: [],
        frequent_hashtags: [],
        top_source: [],
        sentiments: [],
        // advertise: [],
        frequent_entities: {
          organ: [],
          person: [],
          location: [],
          event: [],
        },
      },
      twitter: {
        statistical: [],
        process: [],
        content_type_distribution: [],
        categories: [],
        frequent_words: [],
        frequent_hashtags: [],
        sentiments: [],
        top_source: [],
        frequent_entities: {
          organ: [],
          person: [],
          location: [],
          event: [],
        },
      },
      instagram: {
        statistical: [],
        process: [],
        content_type_distribution: [],
        categories: [],
        frequent_words: [],
        frequent_hashtags: [],
      },
      news: {
        statistical: [],
        process: [],
        categories: [],
        frequent_entities: {
          organ: [],
          person: [],
          location: [],
          event: [],
        },
        frequent_words: [],
        frequent_hashtags: [],
        top_source: [],
      },
    },
    location: {
      overview: {
        description: "",
        instagram_process: [],
        telegram_process: [],
        twitter_process: [],
        news_process: [],
        frequent_entities: {},
      },
      telegram: {
        statistical: [],
        process: [],
        content_type_distribution: [],
        categories: [],
        frequent_words: [],
        frequent_hashtags: [],
      },
      twitter: {
        statistical: [],
        process: [],
        content_type_distribution: [],
        categories: [],
        frequent_words: [],
        frequent_hashtags: [],
        sentiments: [],
        top_source: [],
        frequent_entities: {
          organ: [],
          person: [],
          location: [],
          event: [],
        },
      },
      instagram: {
        statistical: [],
        process: [],
        content_type_distribution: [],
        categories: [],
        frequent_words: [],
        frequent_hashtags: [],
      },
      news: {
        statistical: [],
        process: [],
        categories: [],
        frequent_entities: {
          organ: [],
          person: [],
          location: [],
          event: [],
        },
        frequent_words: [],
        frequent_hashtags: [],
        top_source: [],
      },
    },
  },
};

const use360requestStore = create((set, get) => ({
  ...initialState,

  updateReportField: (path, value) =>
    set((state) => {
      const updatedReport = { ...state?.report };
      const pathParts = path.split(".");
      let current = updatedReport;

      for (let i = 0; i < pathParts.length - 1; i++) {
        const key = pathParts[i];
        if (!current[key] || typeof current[key] !== "object") {
          current[key] = {};
        }
        current = current[key];
      }

      current[pathParts[pathParts.length - 1]] = value;
      return { report: updatedReport };
    }),

  updatePlatformContent: (platform, chartKey, updateData, NER, analysis) => {
    set((state) => {
      const updatedReport = { ...state.report };
      const targetTab = analysis ? "analysis_thoughts" : "resource_overview";

      if (updatedReport.source?.platform[platform]?.[targetTab]) {
        const currentData =
          updatedReport.source.platform[platform][targetTab][chartKey];

        if (Array.isArray(currentData)) {
          updatedReport.source.platform[platform][targetTab][chartKey] =
            Array.isArray(updateData) ? [...updateData] : [updateData];
        } else {
          updatedReport.source.platform[platform][targetTab][chartKey] =
            updateData;
        }
      }

      return { report: updatedReport };
    });
  },

  locationEntityCharts: (data, report_type = false, platform, type360) =>
    set((state) => {
      const updatedReport = { ...state.report };
      if (report_type) {
        updatedReport[type360][platform][report_type] = data;
      } else {
        updatedReport[type360][platform] = {
          ...updatedReport[type360][platform],
          ...data,
        };
      }
      return { report: updatedReport };
    }),

  getChartData: (platform, chartKey) => {
    const state = get();
    return (
      state.report.source.platform[platform]?.resource_overview[chartKey] || {}
    );
  },

  setReportData: (data) => {
    set((state) => {
      const updatedReport = { ...state.report, ...data };
      return { report: updatedReport };
    });
  },

  clearReport: () =>
    set(() => ({
      ...initialState,
    })),
}));

export default use360requestStore;
