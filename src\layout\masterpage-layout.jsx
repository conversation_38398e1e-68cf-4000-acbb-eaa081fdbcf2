import { useLocation, useNavigate } from "react-router-dom";

// Assets
import { SearchBox } from "../components/SearchBox/index.jsx";
import { FilterList } from "../components/FilterList/FilterList.jsx";
import { useEffect, useState } from "react";
import useSearchStore from "../store/searchStore.js";
import { PATHS } from "../constants/index.js";
import PropTypes from "prop-types";

function getLayoutConfig(path) {
  const config = {
    pageName: "",
    searchBox: false,
    filterBox: false,
    isSidebarOpen: false,
    isFilterBoxOpen: false,
  };

  if (path.search(PATHS.dashboard) > 0) {
    config.pageName = "dashboard";
    config.searchBox = true;
    config.isFilterBoxOpen = false;
    return config;
  }

  if (path.search(PATHS.advancedSearch) > 0) {
    config.pageName = "advancedSearch";
    config.searchBox = true;
    config.filterBox = true;
    config.isFilterBoxOpen = true;
    return config;
  }

  if (path.search(PATHS.hotWord) > 0) {
    config.pageName = "hot-word";
    config.searchBox = true;
    config.filterBox = true;
    return config;
  }

  if (path.search(PATHS.hotTopic) > 0) {
    config.pageName = "hot-topic";
    config.searchBox = true;
    config.filterBox = true;
    return config;
  }

  if (path.search(PATHS.bulletin) > 0) {
    config.pageName = "bulletin";
    config.filterBox = true;
    config.searchBox = true;
    config.isFilterBoxOpen = true;
    return config;
  }
}

function useGetLayoutConfig(pathname) {
  const [config, setConfig] = useState({
    searchBox: false,
    filterBox: false,
    clearSearchStore: false,
    isSidebarOpen: false,
    isFilterBoxOpen: false,
  });

  useEffect(() => {
    setConfig(getLayoutConfig(pathname));
  }, [pathname]);

  return config;
}

function MasterPageLayout({
  topSearchBarComponent,
  children,
  overrideFilterSettings = false,
}) {
  const location = useLocation();
  const navigate = useNavigate();
  const config = useGetLayoutConfig(location.pathname);

  const {
    setIsFilterListOpen,
    showFilterList,
    setShowFilterList,
    setShowSearchBox,
    showSearchBox,
    setDoSearch,
  } = useSearchStore();

  const onSearchSubmit = (query) => {
    if (
      config?.pageName === "dashboard" ||
      config?.pageName === "hot-word" ||
      config?.pageName === "hot-topic"
    ) {
      return navigate(PATHS.basePath + PATHS.advancedSearch);
    }
    setDoSearch(true);
  };

  useEffect(() => {
    if (!overrideFilterSettings) {
      setIsFilterListOpen(config.isFilterBoxOpen || true);
      setShowFilterList(config?.filterBox || false);
      setShowSearchBox(config?.searchBox || false);
    }
  }, [config, overrideFilterSettings]);

  return (
    <div className={"flex flex-col h-full w-full [direction:ltr]"}>
      {topSearchBarComponent}
      {showSearchBox && <SearchBox onSubmit={onSearchSubmit} />}
      <div className={"flex flex-row w-full gap-4 px-6"}>
        {showFilterList && <FilterList />}
        {children}
      </div>
    </div>
  );
}

MasterPageLayout.propTypes = {
  topSearchBarComponent: PropTypes.any,
  children: PropTypes.any,
  overrideFilterSettings: PropTypes.bool,
};

export default MasterPageLayout;
