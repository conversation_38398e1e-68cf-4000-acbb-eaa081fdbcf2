import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useReport360Store } from "store/report360Store.js";
import PropTypes from "prop-types";
import { useLocation } from "react-router-dom";

const Report360Create = ({ isUpdate = false, setIsUpdate, isEdit }) => {
  const { step, type } = useReport360Store((state) => state.report);
  const setReport = useReport360Store((state) => state.setReport);
  const { report } = useReport360Store();
  const location = useLocation();
  let breadcrumbConditional =
    location?.pathname?.split("/").at(-1) === "edit"
      ? [
          { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
          {
            title:
              type === "profile"
                ? "استعلام منبع"
                : type === "topic"
                ? "استعلام موجودیت"
                : "استعلام مکان محور",
            link: "/app/report-360/list",
          },
          { title: report.profile.title || "جست‌وجو" },
        ]
      : [
          { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
          { title: "گزارش جدید" },
        ];
  const breadcrumbList = breadcrumbConditional;
  useBreadcrumb(breadcrumbList);

  return (
    <div className="flex-1">
      {/* {step === 1 ? (
        <Step1 nextStep={() => setReport({ step: 2 })} />
      ) : step === 2 &&
        (type === "profile" || type === "location" || type === "topic") ? (
        type === "profile" ? (
          <Step2Profile nextStep={() => setReport({ step: 3 })} />
        ) : type === "location" ? (
          <Step2Location nextStep={() => setReport({ step: 3 })} />
        ) : type === "topic" ? (
          <Step2Person nextStep={() => setReport({ step: 3 })} />
        ) : null
      ) : step === 3 &&
        (type === "profile" || type === "topic" || type === "location") ? (
        type === "profile" ? (
          <Step3Profile
            isUpdate={isUpdate}
            setIsUpdate={setIsUpdate}
            isEdit={isEdit}
          />
        ) : type === "topic" ? (
          <Step3Person isUpdate={isUpdate} />
        ) : type === "location" ? (
          <Step3Person isUpdate={isUpdate} />
        ) : null
      ) : null} */}
    </div>
  );
};

Report360Create.propTypes = {
  isUpdate: PropTypes.bool,
  isEdit: PropTypes.bool,
  setIsUpdate: PropTypes.func,
};

export default Report360Create;
