import { Link, PencilSimpleLine } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";
import user from "../../../../../../assets/images/default.png";

const useImageWithFallback = (src, fallback) => {
  const [image, setImage] = useState(src);

  useEffect(() => {
    if (src) {
      const img = new Image();
      img.src = src;
      img.onload = () => setImage(src);
      img.onerror = () => setImage(fallback);
    } else {
      setImage(fallback);
    }
  }, [src, fallback]);

  return image;
};

const Quote = ({ influence, selectMedia, staticData, nodeMedia, data }) => {
  const avatar = useImageWithFallback(staticData.avatar, user);
  const quoteAvatar = useImageWithFallback(staticData.quoteAvatar, user);

  return (
    <div
      className="border bg-[#f4f4f4] border-low rounded-lg relative"
      onClick={() => window.location.replace(data.postUrl)}
    >
      <div className="p-4 flex flex-col justify-between gap-4 min-h-40 h-full w-full [direction:rtl] relative">
        <div className="flex justify-between">
          <div className="flex gap-4 items-center">
            <div className="flex gap-2 relative">
              <div
                className="size-10 rounded-full bg-contain ml-2 relative z-10"
                style={{
                  backgroundImage: `url(${avatar})`,
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "contain",
                  backgroundPosition: "center center",
                }}
              ></div>
              <div className="flex flex-col flex-1">
                <span className="font-subtitle-medium text-light-neutral-text-high">
                  {staticData.title}
                </span>
                <span className="font-overline-medium text-light-neutral-text-medium">
                  {`${staticData.username}@`}
                </span>
              </div>
            </div>
          </div>
          <div className="flex !items-center gap-2">
            <span
              className="hover:text-blue-400 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                window.open(data.postUrl, "_blank", "noopener,noreferrer");
              }}
            >
              <Link size={19} />
            </span>

            <span className="font-body-small font-bold flex gap-2 bg-[#efd0f5] w-[72px] h-[26px] justify-center rounded-md items-center">
              <PencilSimpleLine color="#DB6DE5" />
              کوت
            </span>
          </div>
        </div>
        <div
          dir="ltr"
          className="font-body-medium text-right"
          style={{ paddingRight: "3rem" }}
        >
          {staticData.content}
        </div>
        <div
          className="flex items-center justify-between"
          style={{ paddingRight: "3rem" }}
        >
          <span
            dir="ltr"
            className="font-overline-medium text-light-neutral-text-medium"
          >
            {new Date(staticData.time).toLocaleString("fa-IR")}
          </span>
          <div className="flex gap-4">
            {selectMedia.twitter.sub.map(({ icon, value }, index) => (
              <div className="flex items-center gap-1" key={index}>
                <span className="font-overline-medium text-light-neutral-text-medium">
                  {value}
                </span>
                <div>{icon}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="flex justify-end w-full">
        <div className="p-4 flex flex-col rounded-lg border border-[#c8cbd6] m-3 mt-1 gap-4 min-h-40 h-full w-[90%] [direction:rtl] relative">
          <div className="flex justify-between">
            <div className="flex gap-4 items-center">
              <div className="flex gap-2 relative">
                <div
                  className="size-10 rounded-full bg-contain ml-2 relative z-10"
                  style={{
                    backgroundImage: `url(${quoteAvatar})`,
                    backgroundRepeat: "no-repeat",
                    backgroundSize: "contain",
                    backgroundPosition: "center center",
                  }}
                ></div>
                <div className="flex flex-col flex-1">
                  <span className="font-subtitle-medium text-light-neutral-text-high">
                    {staticData.quoteTitle}
                  </span>
                  <span className="font-overline-medium text-light-neutral-text-medium">
                    {`${staticData.quoteName}@`}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            dir="ltr"
            className="font-body-medium text-right"
            style={{ paddingRight: "3rem" }}
          >
            {staticData.nodeText}
          </div>
          <div
            className="flex items-center justify-between"
            style={{ paddingRight: "3rem" }}
          >
            <span
              dir="ltr"
              className="font-overline-medium text-light-neutral-text-medium"
            >
              {new Date(staticData.nodeTime).toLocaleString("fa-IR")}
            </span>
            <div className="flex gap-4">
              {nodeMedia.twitter.sub.map(({ icon, value }, index) => (
                <div className="flex items-center gap-1" key={index}>
                  <span className="font-overline-medium text-light-neutral-text-medium">
                    {value}
                  </span>
                  <div>{icon}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

Quote.propTypes = {
  selectMedia: PropTypes.shape({
    twitter: PropTypes.shape({
      avatar: PropTypes.string.isRequired,
      sub: PropTypes.arrayOf(
        PropTypes.shape({
          icon: PropTypes.element.isRequired,
          value: PropTypes.string.isRequired,
        })
      ).isRequired,
    }).isRequired,
  }).isRequired,
  staticData: PropTypes.shape({
    title: PropTypes.string.isRequired,
    username: PropTypes.string.isRequired,
    time: PropTypes.string.isRequired,
  }).isRequired,
};

export default Quote;
