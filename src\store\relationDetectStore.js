import { create } from "zustand";

const initialValue = {
  type: "",
  nodeTitle: "",
  nodeDetails: [],
  edgeDetails: {
    relationType: "direct",
    relationIndex: ["quote", "retweet", "reply", "mention"],
    nodeWeight: 1,
  },
  timeSettings: null,
};

export const useRelationStore = create((set) => ({
  relation: initialValue,
  // Set typ
  setType: (type) =>
    set((state) => ({
      relation: {
        ...state.relation,
        type,
      },
    })),
  // Set nodeDetails (select/deselect logic)
  setNodeDetails: (item) =>
    set((state) => {
      const exists = state?.relation?.nodeDetails?.some(
        (node) => node.id === item.id
      );
      return {
        relation: {
          ...state.relation,
          nodeDetails: exists
            ? state?.relation?.nodeDetails?.filter(
                (node) => node.id !== item.id
              )
            : [...state.relation.nodeDetails, item],
        },
      };
    }),
  setNodeDetailsBulk: (items) =>
    set((state) => {
      return {
        relation: {
          ...state.relation,
          nodeDetails: items,
        },
      };
    }),
  clearNodeDetails: () =>
    set((state) => ({
      relation: {
        ...state.relation,
        nodeDetails: [],
      },
    })),

  setEdgeDetails: (param) =>
    set((state) => ({
      relation: {
        ...state.relation,
        edgeDetails: { ...state.relation.edgeDetails, ...param },
      },
    })),

  setTime: (timeSettings) =>
    set((state) => ({
      relation: {
        ...state.relation,
        timeSettings,
      },
    })),
  resetRelation: () => set(() => ({ relation: initialValue })),
}));
