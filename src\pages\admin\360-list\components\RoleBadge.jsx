import clsx from "clsx";
import PropTypes from "prop-types";

const RoleBadge = ({ role }) => {
  const titleMap = {
    Regular: "کاربر عادی",
    Manager: "مد<PERSON><PERSON>",
    Support: "پشتیبان",
  };

  const stepStateClassName = clsx("font-body-small rounded-[4px] px-1", {
    "text-light-success-text-rest bg-light-success-background-highlight":
      role === "Manager",
    "text-light-inform-text-rest bg-light-inform-background-highlight":
      role === "Regular",
    "text-light-warning-text-rest bg-light-warning-background-highlight":
      role === "Support",
  });
  return <div className={stepStateClassName}>{titleMap[role]}</div>;
};

RoleBadge.propTypes = {
  role: PropTypes.oneOf(["Regular", "Manager", "Support"]),
};

export default RoleBadge;
