import { UserSwitch, <PERSON>L<PERSON> } from "@phosphor-icons/react";
import profile from "/logo_small.png";
import { shortener, toPersianNumber } from "utils/helper";
import { useNavigate } from "react-router-dom";
import { useReport360Store } from "store/report360Store";
import { useState } from "react";
import PopUp from "components/ui/PopUp";

const PioneerUsersTable2 = ({ data }) => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const setReport = useReport360Store((state) => state.setReport);
  const navigate = useNavigate();

  const handleProfileClick = (profile) => {
    try {
      const selectedProfile = {
        id: profile?.id,
        user_title: profile?.title,
        user_name: profile?.key,
        platform: "twitter",
        avatar: profile?.avatar,
      };

      setReport({
        isFromTopSource: true,
        step: 3,
        type: "profile",
        profile: selectedProfile,
      });

      navigate("/app/report-360/create");
    } catch (e) {
      console.error("Profile click error:", e);
    }
  };

  const openConfirmPopup = (item) => {
    setSelectedItem(item);
    setIsPopupOpen(true);
  };

  const closeConfirmPopup = () => {
    setIsPopupOpen(false);
    setSelectedItem(null);
  };

  const submitHandler = () => {
    if (selectedItem) {
      handleProfileClick(selectedItem);
      setIsPopupOpen(false);
    }
  };
  return (
    <>
      <div className="overflow-x-auto scrollbar-thin h-[439px]">
        <table className="min-w-full">
          <thead>
            <tr>
              <th className="px-3 py-2 text-right font-body-medium text-light-neutral-text-medium">
                منبع
              </th>
              {data?.weight && (
                <th className="px-3 py-2 text-center font-body-medium text-light-neutral-text-medium">
                  وزن
                </th>
              )}
              <th className="px-3 py-2 text-center font-body-medium text-light-neutral-text-medium">
                تعداد ارتباط
              </th>
            </tr>
          </thead>
          <tbody className="font-subtitle-medium">
            {data?.length > 0 ? (
              data.map((row) => (
                <tr
                  key={row?.node_id || Math.random()} // Fallback key if node_id is missing
                  className={`hover:bg-gray-50 cursor-pointer} cursor-pointer`}
                  onClick={() => openConfirmPopup(row)}
                >
                  <td className="flex items-center gap-1 pl-4 py-2 font-body-medium text-light-neutral-text-medium">
                    <div className="relative">
                      <img
                        src={row?.node_avatar || profile || row?.avatar}
                        alt="profile"
                        width={35}
                        className="border rounded-full"
                        onError={(e) => (e.target.src = profile)}
                      />
                      <div className="bg-black text-white p-1 rounded-full absolute top-6 right-0">
                        <XLogo size={12} />
                      </div>
                    </div>
                    <div className="grid">
                      <p className="font-subtitle-medium text-black leading-4">
                        {toPersianNumber(
                          shortener(
                            row?.node_title || row?.title || "",
                            10,
                            "rtl"
                          )
                        ) || "نامشخص"}
                      </p>
                      <p className="font-overline-medium text-light-neutral-text-medium">
                        {toPersianNumber(
                          shortener(row?.node_name || row?.key || "", 10, "rtl")
                        ) || "نامشخص"}
                      </p>
                    </div>
                  </td>
                  {data?.weight && (
                    <td className="px-3 py-2 font-body-medium text-light-neutral-text-medium text-center">
                      {toPersianNumber(row?.node_weight ?? 0)}
                    </td>
                  )}
                  <td className="px-3 py-2 font-body-medium text-light-neutral-text-medium text-center">
                    {toPersianNumber(row?.node_link || row?.count)}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={3}
                  className="py-4 text-center font-body-bold-large text-light-neutral-text-medium"
                >
                  موردی یافت نشد!
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <PopUp
        isOpen={isPopupOpen}
        onClose={closeConfirmPopup}
        submitHandler={submitHandler}
        title="آیا می‌خواهید گزارش های 360 این منبع نمایش داده شود؟"
        agreeButton="بله"
        cancleButton="خیر"
        icon={<UserSwitch size={45} />}
      >
        <p className="py-5 font-body-medium">
          توجه کنید که با کلیک برروی گزینه بله به صفحه گزارشات 360 این منبع
          منتقل خواهید شد.
        </p>
      </PopUp>
    </>
  );
};

export default PioneerUsersTable2;
