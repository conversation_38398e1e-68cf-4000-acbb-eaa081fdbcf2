import { useState, useEffect } from "react";
import ReportsType from "./components/ReportsType";
import ReportsInfo from "./components/ReportsInfo";
import OverviewTab from "./overviewTab";
import ContactsTab from "./contactsTab";
import AnalysisTab from "./analysisTab";
import UserInfo from "./components/userInfo";
import SourceContent from "./source-content";
import ProfileSelector from "./components/ProfileSelector";
import use360requestStore from "store/360requestStore";
import CardLoading from "components/ui/CardLoading.jsx";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import { Link, useParams } from "react-router-dom";
import PLATFORMS from "constants/platforms";
import { ToastContainer } from "react-toastify";

const Step3Profile = ({ isEdit }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState();
  const [activeSpan, setActiveSpan] = useState("overview");
  const [isFixed, setIsFixed] = useState(false);
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const { id, platform } = useParams();

  const fetchInfo = async () => {
    setLoading(true);
    try {
      const filters = {
        q: id,
        platform: platform,
        report_type: "source_info",
      };
      const infoQuery = buildRequestData(filters, "source_info");
      const response = await advanceSearch.search(infoQuery);

      const data = response?.data?.data?.[platform] || null;
      if (!data) return setError("not-found");
      updateReportField("content.source_info", data);
    } catch (e) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!Object.values(PLATFORMS).includes(platform))
      return setError("wrong-platform");

    updateReportField("content.report_platform", platform);
    fetchInfo();
  }, [id, platform]);

  useEffect(() => {
    const handleScroll = () => {
      const offset = 100;
      if (window.scrollY > offset) {
        setIsFixed(true);
      } else {
        setIsFixed(false);
      }
    };

    if (activeSpan !== "analysis" && activeSpan !== "source-content") {
      window.addEventListener("scroll", handleScroll);
    }

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [activeSpan]);

  // todo: should remove
  // const sourceReport = use360requestStore((state) => state.report);
  // useEffect(() => {
  //   console.log(sourceReport);
  // }, [sourceReport]);

  if (error === "wrong-platform") {
    return (
      <div className="w-full h-[640px] flex flex-col justify-center items-center">
        <div className="size-12 bg-[url(/empty_folder.png)] bg-no-repeat bg-contain bg-center mb-4"></div>
        <p className="font-body-large mb-2">!خطا! بستر انتخابی نادرست است</p>
        <p className="font-body-medium">
          بازگشت به
          <Link
            to="/app/report-360/list"
            className="underline underline-offset-4 text-light-primary-text-rest cursor-pointer"
          >
            {" "}
            لیست گزارش‌ها{" "}
          </Link>
        </p>
      </div>
    );
  }
  if (error === "not-found") {
    return (
      <div className="w-full h-[640px] flex flex-col justify-center items-center">
        <div className="size-12 bg-[url(/empty_folder.png)] bg-no-repeat bg-contain bg-center mb-4"></div>
        <p className="font-body-large mb-2">منبع مورد نظر یافت نشد!</p>
        <p className="font-body-medium">
          بازگشت به
          <Link
            to="/app/report-360/list"
            className="underline underline-offset-4 text-light-primary-text-rest cursor-pointer"
          >
            {" "}
            لیست گزارش‌ها{" "}
          </Link>
        </p>
      </div>
    );
  }
  if (loading)
    return (
      <div className="w-full h-[640px]">
        <CardLoading />
      </div>
    );
  return (
    <>
      <div className="px-5 w-[100%] flex flex-col">
        <ProfileSelector />
        <div
          className={
            activeSpan !== "source-content" && isFixed
              ? "fixed w-[85%] z-10"
              : "z-10"
          }
        >
          {activeSpan !== "source-content" && isFixed && (
            <div className="absolute top-[-20px] left-0 w-full h-[calc(90%+20px)] bg-gradient-to-t from-white via-white/90 to-transparent backdrop-blur-md" />
          )}
          <UserInfo isEdit={isEdit} isFixed={isFixed} />
        </div>
        <ReportsType
          activeTab={activeSpan}
          isFixed={isFixed}
          setIsFixed={setIsFixed}
          activeSpan={activeSpan}
          setActiveSpan={setActiveSpan}
        />
        {/* <ReportsInfo
          activeSpan={activeSpan}
          sort={sort}
          setSort={setSort}
          onDropdownOpenChange={handleDropdownState}
        /> */}
        <div>
          <div className={activeSpan !== "overview" ? "hidden" : "block"}>
            <OverviewTab />
          </div>
          {/* <div className={activeSpan !== "contacts" ? "hidden" : "block"}>
            <ContactsTab isUpdate={isUpdate} setIsUpdate={setIsUpdate} />
          </div> */}
          <div className={activeSpan !== "analysis" ? "hidden" : "block"}>
            <AnalysisTab />
          </div>
          <div className={activeSpan !== "source-content" ? "hidden" : "block"}>
            <SourceContent />
          </div>
          {/* {activeSpan === "contacts" && <ContactsTab />}
          {activeSpan === "analysis" && <AnalysisTab isUpdate={isUpdate} />}
          {activeSpan === "source-content" && (
            <SourceContent
              isUpdate={isUpdate}
              sort={sort}
              setSort={setSort}
              setIsDropdownOpen={setIsDropdownOpen}
              isDropdownOpen={isDropdownOpen}
            />
          )} */}
        </div>
      </div>
    </>
  );
};

export default Step3Profile;
