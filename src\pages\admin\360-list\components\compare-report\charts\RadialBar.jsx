import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";

const RadialBar = ({
  data,
  legendEnabled = false,
  legendFormatter = () => {},
}) => {
  const selectColor = ["#201089", "#2916B6", "#462EE5", "#8171EF", "#C1B8FA"];

  const options = {
    chart: {
      type: "bar",
      inverted: true,
      polar: true,
      height: 300,
      backgroundColor: "transparent",
    },
    title: {
      text: null,
      align: "left",
    },
    pane: {
      size: "90%",
      innerSize: 0,
      endAngle: 270,
    },
    credits: {
      enabled: false,
    },
    legend: {
      enabled: legendEnabled,
      useHTML: true,
      labelFormatter: legendFormatter,
    },
    xAxis: {
      categories: data.map(
        (item) =>
          `<div style="display:flex; align-items:center; gap:5px; padding:0 30px; word-break:break-all;">
               <span>@${item.username}</span>
               <img src="${item.avatar}" alt="${item.username}" style="width:20px; height:20px; border-radius:50%;" />
             </div>`,
      ),
      labels: {
        useHTML: true,
        align: "right",
        style: {
          fontSize: "12px",
          fontFamily: "iranyekan",
        },
      },
      lineWidth: 0,
      gridLineWidth: 0,
    },
    yAxis: {
      visible: false,
    },
    tooltip: {
      headerFormat: "",
      backgroundColor: "#efefef",
      shadow: false,
      useHTML: true,
      formatter: function () {
        return `<div style="text-align:center;font-family:iranyekan,serif;">
                  <div>${this.key}</div>
                  <div>${toPersianNumber(this.y)}</div>
                </div>`;
      },
    },
    plotOptions: {
      bar: {
        borderRadius: 8, // Rounded edges for the bars
        groupPadding: 0.2,
        pointPadding: 0.1,
        dataLabels: {
          enabled: true,
          formatter: function () {
            return `<span">${toPersianNumber(this.y)}</span>`;
          },
          style: {
            fontFamily: "iranyekan",
            fontSize: "12px",
            // textOutline: "none",
            // fontWeight: 400,
          },
          align: "center",
        },
      },
    },
    series: [
      {
        name: "User Data",
        data: data.map((item, index) => ({
          y: item.y,
          name: item.username ? `@${item.username}` : item.title,
          color: selectColor[index % selectColor.length],
        })),
      },
    ],
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default RadialBar;
