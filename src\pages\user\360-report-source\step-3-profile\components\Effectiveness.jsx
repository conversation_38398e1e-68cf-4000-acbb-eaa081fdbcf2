import { useEffect, useRef, useState } from "react";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import GraphChart from "./GraphChart";
import {
  At,
  PencilSimpleLine,
  Repeat,
  SpinnerGap,
} from "@phosphor-icons/react";
import { useReport360Store } from "store/report360Store";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import user from "../../../../../assets/images/default.png";
import PropTypes from "prop-types";
import Popup from "./popUp";
import ListDrawer from "./ListDrawer";
import TextWrapper from "./TextWrapper";
import NodeDrawer from "./NodeDrawer";
import { toPersianNumber } from "utils/helper";
import ExportMenu from "components/ExportMenu/index.jsx";
import use360requestStore from "store/360requestStore.js";

const Effectiveness = ({ isFullScreen }) => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
    impact_graph: state.report?.content?.report_info?.impact_graph,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const platform = sourceReport.platform;
  const profile = sourceReport.profile;
  const sourceId =
    profile.user_name ||
    profile.source_name ||
    profile.channel_id ||
    profile.id;

  const [postCounts, setPostCounts] = useState({
    repost: 0,
    quote: 0,
    mention: 0,
  });
  const [graphData, setGraphData] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  const [loading, setLoading] = useState(false);
  const [nodeInfo, setNodeInfo] = useState(null);
  const [impactData, setImpactData] = useState(null);
  const [totalCounts, setTotalCounts] = useState(0);
  const [showMore, setShowMore] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [isCentralNodeSelected, setIsCentralNodeSelected] = useState(false);
  const [quote, setQuote] = useState(false);
  const [selectedListNode, setSelectedListNode] = useState(null);
  const graphRef = useRef();

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Repost Count",
      data: graphData.map((node) => node.fields?.repost || 0),
      time: graphData.map(
        (node) => node.to || node.profile_info?.user_name || "Unknown"
      ),
    },
    {
      name: "Quote Count",
      data: graphData.map((node) => node.fields?.quote || 0),
      time: graphData.map(
        (node) => node.to || node.profile_info?.user_name || "Unknown"
      ),
    },
    {
      name: "Mention Count",
      data: graphData.map((node) => node.fields?.mention || 0),
      time: graphData.map(
        (node) => node.to || node.profile_info?.user_name || "Unknown"
      ),
    },
  ];

  const time = graphData.map(
    (node) => node.to || node.profile_info?.user_name || "Unknown"
  );

  const getData = async () => {
    setLoading(true);
    try {
      const filters = {
        date,
        platform,
        sources: [sourceId.toString()],
      };

      const infoQuery = buildRequestData(filters, "impact_graph", 20);
      const response = await advanceSearch.search(infoQuery);
      const data = response.data.data?.[platform];
      if (!data) return [];

      const graphDataResult = Object.entries(data)
        .slice(0, 35)
        .map(([key, value]) => {
          if (key === profile.user_name) return null;

          const {
            repost = 0,
            quote = 0,
            mention = 0,
            profile_info,
            content_query,
          } = value;

          const totalCount = repost + quote + mention;
          const fields = {};

          if (repost > 0) fields.repost = repost;
          if (quote > 0) fields.quote = quote;
          if (mention > 0) fields.mention = mention;

          return {
            from: profile.user_title,
            to: profile_info?.user_title || key,
            count: totalCount,
            fields,
            profile_info: profile_info,
            content_query: content_query,
            avatar: profile_info?.avatar || profile_info?.original_avatar,
          };
        })
        .filter(Boolean);

      updateReportField("content.report_info.impact_graph", {
        impactData: data,
        graphData: graphDataResult,
      });
      setImpactData(data);
      setGraphData(graphDataResult);
    } catch (error) {
      console.error("Error fetching data:", error);
      setGraphData([]);
      setImpactData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleNodeSelect = (clickedNode, node_info, node) => {
    setSelectedNode(node);
    setSelectedItem({
      ...node_info,
      platform: platform,
    });
    setShowPopup(true);
    setIsCentralNodeSelected(false);
  };

  const handleLinkSelect = async (content_query, profile_info, node_count) => {
    setTotalCounts(node_count);
    setShowMore(true);
    setSelectedListNode({ content_query, profile_info, count: node_count });
    const filters = {
      ...content_query,
      sort_type: content_query.order,
      date: {
        from: content_query.start_date,
        to: content_query.end_date,
      },
    };

    const infoQuery = buildRequestData(filters, filters?.report_type, 20);
    const response = await advanceSearch.search(infoQuery);
    const responseDataArray = response?.data?.data?.[platform] || [];

    const combinedDataArray = responseDataArray?.map((responseData) => {
      return {
        avatar: profile_info?.avatar || responseData?.quote?.avatar || user,
        originalAvatar: profile_info?.original_avatar || null,
        followerCount: profile_info?.follower_count || null,
        userName: profile_info?.user_name || null,
        quote: responseData?.quote || null,
        postType: responseData?.post_type || null,
        text: responseData?.text || null,
        userTitle: profile_info?.user_title || null,
        centralNodeAvatar: profile?.avatar || null,
        centralNodeTitle: profile?.user_title || null,
        centralNodeUsername: profile?.user_name || null,
        centralNodeFollower: profile?.follower_count || null,
        quoteName: responseData?.quote?.user_name,
        quoteTitle: responseData?.quote?.user_title,
        quoteAvatar: responseData?.quote?.avatar,
        nodeCount: node_count,
        totalCount: totalCounts,
      };
    });

    setPostCounts({
      quote: impactData?.[profile_info.user_name]?.quote || 0,
      repost: impactData?.[profile_info.user_name]?.repost || 0,
      mention: impactData?.[profile_info.user_name]?.mention || 0,
    });

    setNodeInfo(combinedDataArray);

    setQuote(
      responseDataArray.map((responseData) => ({
        ...responseData,
        avatar: responseData?.avatar || user || null,
        centralNodeAvatar: profile?.original_avatar || profile?.avatar || null,
        clickedNodeTitle: profile_info?.user_title || null,
        clickedNodeUsername: profile_info?.user_name || null,
        quoteName: responseData?.quote?.user_name,
        quoteTitle: responseData?.quote?.user_title,
        quoteAvatar: responseData?.quote?.avatar,
        postUrl: responseData?.post_url,
        platform,
      }))
    );
  };

  const handleCenterNode = () => {
    setSelectedItem({
      ...profile,
      platform: platform,
    });
    setShowPopup(true);
    setIsCentralNodeSelected(true);
  };

  const post_type_badges = [
    {
      name: "ری‌پست",
      engName: "repost",
      icon: <Repeat color="#FF9408" />,
      count: postCounts?.repost,
      bgColor: "#f9dbb2",
    },
    {
      name: "کوت",
      engName: "quote",
      icon: <PencilSimpleLine color="#DB6DE5" />,
      count: postCounts?.quote,
      bgColor: "#efd0f5",
    },
    {
      name: "منشن",
      engName: "mention",
      icon: <At color="#1DCEA3" />,
      count: postCounts?.mention,
      bgColor: "#b6ede1",
    },
  ];

  const handleShowDrawer = () => {
    if (selectedNode) {
      handleLinkSelect(
        selectedNode.content_query,
        selectedNode.profile_info,
        selectedNode.count
      );
    }
    setShowPopup(false);
  };

  useEffect(() => {
    if (
      sourceReport.impact_graph &&
      sourceReport.impact_graph.impactData &&
      sourceReport.impact_graph.graphData &&
      sourceReport.date.from === date.from &&
      sourceReport.date.to === date.to
    ) {
      setImpactData(sourceReport.impact_graph.impactData);
      setGraphData(sourceReport.impact_graph.graphData);
    } else {
      getData();
      updateReportField("start_date", date.from);
      updateReportField("end_date", date.to);
    }
  }, [date]);

  useEffect(() => {
    if (!showMore) {
      setSelectedListNode(null);
    }
  }, [showMore]);

  return (
    <div className="flex gap-3 w-full">
      <Card
        className={`flex effectiveness-container bg-white flex-col gap-2 ${
          isFullScreen ? "!w-full" : "!w-[68%]"
        } h-[37rem] flex-shrink-0 flex-grow-0`}
      >
        <div className="flex justify-between w-full items-center px-3">
          <Title title="گراف تاثیرگذاری" pos="top">
            در این گراف نشان‌ می‌دهد که این منبع چه میزان اثربخشی بر روی سایرین
            داشته است
          </Title>
          <ExportMenu
            chartSelector=".effectiveness-container"
            fileName="effectiveness-graph"
            series={series}
            chartRef={graphRef}
            time={time}
            excelHeaders={[
              "User",
              "Repost Count",
              "Quote Count",
              "Mention Count",
            ]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
            useCanvasExport={true}
            chartTitle="گراف تاثیرگذاری"
          />
        </div>
        {loading ? (
          <div className="flex h-full">
            <Card className="flex flex-col gap-4 w-full h-full">
              <div className="flex w-full h-full justify-center items-center">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            </Card>
          </div>
        ) : graphData?.length > 0 ? (
          <GraphChart
            chartRef={graphRef}
            data={graphData}
            isFullScreen={isFullScreen}
            user={user}
            onLinkSelect={handleLinkSelect}
            onNodeSelect={handleNodeSelect}
            onCenterNodeSelect={handleCenterNode}
            isReverse={false}
            selectedListNode={selectedListNode}
          />
        ) : (
          <div className="h-full flex items-center justify-center font-subtitle-medium">
            داده‌ای برای نمایش وجود ندارد
          </div>
        )}
      </Card>
      <Card
        className={`!w-[31%] h-[37rem] scrollbar-thin flex-shrink-0 flex-grow-0 overflow-y-auto overflow-x-hidden ${
          isFullScreen ? "hidden" : "block"
        }`}
      >
        <div className="w-full pt-0">
          <h3 className="font-bold text-lg mb-4">
            لیست کاربران مرتبط با گراف تاثیرگذاری
          </h3>
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          ) : graphData.length > 0 ? (
            <div>
              <div className="grid grid-cols-[1fr_auto] items-center gap-4 px-1 py-2 border-b border-gray-200">
                <p className="font-subtitle-small text-gray-700 pr-2">
                  کاربران
                </p>
                <p className="font-subtitle-small text-gray-700">وزن ارتباطی</p>
              </div>
              <ul className="space-y-3">
                {graphData
                  .slice()
                  .sort((a, b) => b.count - a.count)
                  .map((node, index) => (
                    <li
                      key={index}
                      className="flex w-full gap-2 items-center hover:bg-gray-100 p-1 cursor-pointer rounded"
                      onClick={() => {
                        handleLinkSelect(
                          node.content_query,
                          node.profile_info,
                          node.count
                        );
                      }}
                    >
                      <img
                        src={node.avatar || user}
                        alt={node.to}
                        className="w-9 h-9 rounded-full mr-2"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = user;
                        }}
                      />
                      <div className="w-full grid grid-cols-[1fr_auto] items-center gap-4">
                        <div className="flex flex-col gap-1 overflow-hidden">
                          <p className="font-medium truncate">{node.to}</p>
                          <p className="text-sm font-body-small text-gray-500 truncate">
                            {node.profile_info.user_name}@
                          </p>
                        </div>
                        <p className="text-sm font-body-small text-gray-500 pl-4">
                          {toPersianNumber(node.count)}
                        </p>
                      </div>
                    </li>
                  ))}
              </ul>
            </div>
          ) : (
            <p className="h-full flex items-center justify-center font-subtitle-medium">
              ارتباطی برای نمایش وجود ندارد
            </p>
          )}
        </div>
      </Card>
      {showMore && nodeInfo && nodeInfo.length > 0 && (
        <NodeDrawer
          quote={quote}
          badges={post_type_badges}
          nodeInfo={nodeInfo}
          totalCounts={totalCounts}
          setShowMore={setShowMore}
        />
      )}
      {showPopup && (
        <div className="!z-[999]">
          <Popup isOpen={showPopup} onClose={() => setShowPopup(false)}>
            <div className="!min-w-[30rem] max-w-[40rem]">
              <div className="flex flex-col gap-4">
                <ListDrawer
                  data={selectedItem}
                  media={selectedItem?.platform}
                  fallbackImg={user}
                  showHeaderMenu={false}
                  isCentralNodeSelected={isCentralNodeSelected}
                  onShowDrawer={handleShowDrawer}
                >
                  <TextWrapper media={selectedItem?.platform}>
                    {selectedItem?.platform === "news" && selectedItem.content
                      ? selectedItem.content
                      : selectedItem.bio}
                  </TextWrapper>
                </ListDrawer>
              </div>
            </div>
          </Popup>
        </div>
      )}
    </div>
  );
};

Effectiveness.propTypes = {
  isFullScreen: PropTypes.bool.isRequired,
  isUpdate: PropTypes.bool,
};

export default Effectiveness;
