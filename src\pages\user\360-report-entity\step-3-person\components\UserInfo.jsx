import { Card } from "components/ui/Card";
import { useReport360Store } from "store/report360Store";
import DateFilter from "./DateFilter";
import { CButton } from "components/ui/CButton";
import { notification } from "utils/helper";
import use360requestStore from "store/360requestStore";
import report360 from "service/api/report360";
import { useEffect, useState, useRef, memo } from "react";
import PropTypes from "prop-types";
import {
  CheckCircle,
  User,
  WarningDiamond,
  Image,
} from "@phosphor-icons/react";
import Popup from "components/ui/PopUp";
import { CInput } from "components/ui/CInput";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import FetchImage from "pages/user/show-profile/components/FetchImage";
import Loading from "components/ui/Loading";
import { useNavigate } from "react-router-dom";

const PersonUserInfo = ({ isEdit }) => {
  const { date, id } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    entity: state.report?.content?.source_info.entity,
  }));

  const entity = sourceReport.entity;

  const setReport = useReport360Store((state) => state.setReport);
  const updateReport = use360requestStore((state) => state.updateReportField);
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState(entity || "");
  const [loadingAvatar, setLoadingAvatar] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(null);
  const [backgroundImage, setBackgroundImage] = useState(null);
  const fileInputRef = useRef(null);

  const navigate = useNavigate();

  const breadcrumbList = [
    { title: "گزارش ۳۶۰", link: "/app/report-360/list" },
    {
      title: "استعلام موجودیت",
      link: "/app/report-360/create",
    },
    { title: entity },
  ];
  useBreadcrumb(breadcrumbList);

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setReport({ date: { from: from, to: to } });
  };

  const handleTitle = (e) => {
    setTitle(e.target.value);
    updateReport("title", e.target.value);
  };

  const handleAvatarClick = () => fileInputRef.current.click();

  const handleFileChange = async (event) => {
    setLoadingAvatar(true);
    const file = event.target.files[0];

    if (!file) {
      setLoadingAvatar(false);
      return;
    }

    const maxSizeInBytes = 1 * 1024 * 1024; // 1 MB

    if (file.size > maxSizeInBytes) {
      notification.error(
        "حجم فایل انتخاب شده نباید بیشتر از 1 مگابایت باشد.",
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
      setLoadingAvatar(false);
      return;
    }

    if (file.type === "image/png" || file.type === "image/jpeg") {
      try {
        const formData = new FormData();
        formData.append("image", file);

        const uploadedImageRes = await report360.image(formData);
        const avatarUrl = uploadedImageRes.data.data.image;

        const imageUrl = URL.createObjectURL(file);
        setBackgroundImage(imageUrl);

        setAvatarUrl(avatarUrl);
      } catch (error) {
        notification.error(
          "آپلود تصویر با خطا مواجه شد.",
          <WarningDiamond size={32} className="text-light-error-text-rest" />
        );
      }
    } else {
      notification.error(
        "فرمت فایل انتخاب شده صحیح نیست.",
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
    }

    setLoadingAvatar(false);
  };

  const handleReportSave = async () => {
    try {
      let response;
      updateReport("title", title);
      if (avatarUrl !== null) {
        updateReport("avatar", avatarUrl);
      }
      const reportState = use360requestStore.getState().report;
      if (isEdit === true) {
        response = await report360.update(id, reportState);
        if (response?.data?.status === "OK") {
          notification.success(
            `گزارش ۳۶۰ با موفقیت بروزرسانی شد`,
            <CheckCircle className="text-light-success-text-rest" />
          );
        }
      } else {
        response = await report360.create(reportState);
        if (response?.data?.status === "OK") {
          notification.success(
            `گزارش ۳۶۰ با موفقیت ذخیره شد`,
            <CheckCircle className="text-light-success-text-rest" />
          );
          setTimeout(() => navigate(`/app/report-360/list`), 2000);
        }
      }
      setIsOpen(false);
    } catch (error) {
      notification.error(
        `با عرض پوزش، امکان ذخیره 'گزارش' وجود ندارد. لطفا پس از چند لحظه دوباره تلاش کنید و در صورت عدم حل مشکل با پشتیبانی تماس بگیرید.`
      );
      console.error("Error saving report:", error);
    }
  };

  // useEffect(() => {
  //   const { from, to } = date;
  //   updateReport("start_date", from);
  //   updateReport("end_date", to);
  //   updateReport("report_type", "entity");
  // }, [date, entity]);

  const handleKeyDown = async (e) => {
    if (e.key === "Enter") {
      await handleReportSave();
    }
  };

  return (
    <>
      <Card
        className={`flex justify-between items-center !px-6 gap-5 border-r-8 border-[#4D36BF] shadow-xl card-animation card-delay mb-4 !w-[98%] mx-3`}
      >
        <h3 className="font-subtitle-large">{entity}</h3>
        <div>
          <div className="flex pt-1 gap-10 z-[10]">
            <DateFilter
              handleDateChange={handleDateChange}
              selectedDateRange={date}
            />
            <CButton
              type={"submit"}
              onClick={() => (!isEdit ? setIsOpen(true) : handleReportSave())}
              size={"lg"}
              className={"[direction:rtl] [width:166px!important]"}
            >
              {isEdit ? "بروزرسانی استعلام" : "ذخیره استعلام"}
            </CButton>
          </div>
        </div>
      </Card>
      <Popup
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title={"ذخیره گزارش ۳۶۰"}
        submitHandler={handleReportSave}
      >
        <div className="flex flex-col gap-6">
          <div className={"flex justify-center mb-4"}>
            <FetchImage
              imageUrl={backgroundImage}
              onFetchSuccess={setBackgroundImage}
            />
            <div
              className={
                "flex relative cursor-pointer w-[120px] h-[120px] bg-light-neutral-background-medium" +
                " items-center align-middle text-center group bg-center bg-no-repeat bg-contain"
              }
              style={{
                clipPath: "circle(50%)",
                backgroundImage: backgroundImage
                  ? `url(${backgroundImage})`
                  : "none",
              }}
              onClick={handleAvatarClick}
            >
              {!loadingAvatar ? (
                !backgroundImage && (
                  <User
                    className={"w-full text-light-primary-text-rest"}
                    size={40}
                  />
                )
              ) : (
                <Loading className={"relative w-full h-full"} />
              )}
              <div
                className={
                  "flex w-full absolute bottom-0 bg-light-primary-background-highlight text-light-neutral-text-low h-[22px] text-center opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100"
                }
              >
                <Image size={20} style={{ margin: "0 auto" }} />
              </div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                max={"1m"}
                onChange={handleFileChange}
              />
            </div>
          </div>
          <div className="font-overline-large">
            این گزارش را با نتایج موجود ذخیره کنید تا در آینده بتوانید به آن
            رجوع کنید.
          </div>
          <div>
            <CInput
              title="عنوان گزارش"
              type="text"
              onChange={handleTitle}
              value={title}
              placeholder={`برای گزارش ۳۶۰ موجودیت یک عنوان بنویسید`}
              inputProps={{ onKeyDown: handleKeyDown }}
            />
          </div>
        </div>
      </Popup>
    </>
  );
};

PersonUserInfo.propTypes = {
  isEdit: PropTypes.bool.isRequired,
};

export default memo(PersonUserInfo);
