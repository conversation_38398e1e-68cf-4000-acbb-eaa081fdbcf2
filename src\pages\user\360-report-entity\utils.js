import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";

export const fetchStatisticalData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ date, platform, q }, "statistical");
    const res = await advanceSearch.search(requestData);
    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };
    return platformData;
  } catch (error) {
    console.error("Statistical Data Fetch Error:", error);
    throw error;
  }
};

export const fetchProcessData = async ({ date, q, platform = "all" }) => {
  try {
    const requestData = buildRequestData({ q, date, platform }, "process");
    const res = await advanceSearch.search(requestData);

    const platformData = {
      twitter: res?.data?.data?.twitter || [],
      instagram: res?.data?.data?.instagram || [],
      telegram: res?.data?.data?.telegram || [],
      news: res?.data?.data?.news || [],
    };

    return platformData;
  } catch (error) {
    console.error("Process Data Fetch Error:", error);
    throw error;
  }
};

export const fetchReport360EntityData = async ({
  date,
  q,
  platform = "all",
}) => {
  try {
    const [statisticalData, processData] = await Promise.all([
      fetchStatisticalData({ date, q, platform }),
      fetchProcessData({ date, q, platform }),
    ]);

    return {
      statistical: statisticalData,
      process: processData,
    };
  } catch (error) {
    console.error("Report 360 Entity Data Fetch Error:", error);
    throw error;
  }
};

export const isCachedDataValid = (cachedDate, currentDate) => {
  return (
    cachedDate?.from === currentDate?.from && cachedDate?.to === currentDate?.to
  );
};

export const hasRequiredCachedData = (sourceReport) => {
  return sourceReport?.statistical?.twitter && sourceReport?.process?.twitter;
};
