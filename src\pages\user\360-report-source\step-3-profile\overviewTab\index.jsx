import { useReport360Store } from "store/report360Store";
import AiAnalysis from "../components/Ai-Analysis";
import ContentDistribution from "../components/ContentDistribution";
import ContentNumber from "../components/ContentNumber";
import Excitement from "../components/Excitement";
import Info from "../components/Info";
import ReleaseChartContainer from "../components/Release";
import RepeatHashtags from "../components/RepeatHashtags";
// import RepeatMentions from "../components/RepeatMentions";
import RepeatNames from "../components/RepeatNames";
import RepeatWords from "../components/RepeatWords";
import Sentiment from "../components/Sentiment";
import ThematicCategory from "../components/ThematicCategory";
import SimilarSources from "../components/SimilarSources";
import ThoughtLine from "../components/ThoughtLine";
import PoliticalSpectrum from "../components/Political";
import PropTypes from "prop-types";
import { memo } from "react";

const OverviewTab = ({ isUpdate, setIsUpdate }) => {
  const { profile } = useReport360Store((state) => state.report);
  const platform = profile.platform;

  console.log("foo");

  return platform === "telegram" ? (
    <div className="flex flex-col gap-3 h-full">
      <div className="flex gap-3 h-full">
        <div className="w-[60%] flex flex-col gap-3">
          <div className="flex-none">
            <Info isUpdate={isUpdate} />
          </div>
          <div className="flex flex-col gap-3 flex-1">
            <div className="flex-1">
              <ReleaseChartContainer isUpdate={isUpdate} />
            </div>
            <div className="flex gap-3 flex-1">
              <div className="w-1/2 flex">
                <div className="flex-1">
                  {platform !== "news" && <Sentiment />}
                </div>
              </div>
              <div className="w-1/2 flex">
                <div className="flex-1">
                  {platform !== "news" && <Excitement isUpdate={isUpdate} />}
                </div>
              </div>
            </div>
            <div className="flex-1 !h-full">
              <RepeatNames isUpdate={isUpdate} setIsUpdate={setIsUpdate} />
            </div>
          </div>
        </div>
        <div className="w-[40%] flex flex-col gap-3">
          <div className="flex-1">
            <AiAnalysis />
          </div>
          <div className="flex-1">
            <ContentNumber />
          </div>
          <div>{platform !== "news" && <ContentDistribution />}</div>
          {/* <div>
            <RepeatWords />
          </div> */}
          <div className="flex-1">
            {platform !== "news" && <RepeatHashtags />}
          </div>
        </div>
      </div>
      <div className="flex-1">
        <ThematicCategory />
      </div>
    </div>
  ) : platform === "news" ? (
    <div className="flex flex-col gap-3">
      <div className="flex gap-3 flex-row w-full">
        <div className="w-3/5">
          <Info isUpdate={isUpdate} />
        </div>
        <div className="w-2/5">
          <RepeatNames isUpdate={isUpdate} setIsUpdate={setIsUpdate} />
        </div>
      </div>
      <div className="flex gap-3">
        <div className="w-[60%] flex flex-col gap-3">
          <div className="flex-1 flex flex-col">
            <SimilarSources />
          </div>
        </div>

        <div className="w-[40%] flex flex-col gap-3">
          <div className="flex-1 flex flex-col">
            <ThoughtLine />
          </div>
          <div className="flex-1 flex flex-col">
            <PoliticalSpectrum isUpdate={isUpdate} />
          </div>
        </div>
      </div>
      <ReleaseChartContainer isUpdate={isUpdate} isNews={true} />

      <div className="flex gap-3 flex-row w-full">
        <div className="w-3/5">
          <ThematicCategory />
        </div>
        <div className="w-2/5">
          <RepeatWords />
        </div>
      </div>
    </div>
  ) : (
    <div className="flex gap-3 h-full">
      <div className="w-[60%] flex flex-col gap-3">
        <div className="flex-none">
          <Info />
        </div>
        <div className="flex flex-col gap-3 flex-1">
          <div className="flex-1">
            <ReleaseChartContainer />
          </div>
          <div className="flex gap-3 flex-1">
            <div className="w-1/2 flex">
              <div className="flex-1">
                {platform !== "news" && <Sentiment />}
              </div>
            </div>
            <div className="w-1/2 flex">
              <div className="flex-1">
                {platform !== "news" && <Excitement />}
              </div>
            </div>
          </div>
          <div className="flex-1">
            <ThematicCategory />
          </div>
          {/* <div className="flex-1">
            {!(platform === "news" || platform === "telegram") && (
              <RepeatMentions />
            )}
          </div> */}
        </div>
      </div>
      <div className="w-[40%] flex flex-col gap-3">
        <div className="flex-1">
          <AiAnalysis />
        </div>
        <div className="flex-1">
          <ContentNumber />
        </div>
        {/* <div className="flex-1">
          {platform !== "news" && <ContentDistribution />}
        </div> */}
        {/* <div className="flex-1">
          <RepeatWords isUpdate={isUpdate} />
        </div> */}
        <div className="flex-1">
          {platform !== "news" && <RepeatHashtags />}
        </div>
        <div className="flex-1">
          <RepeatNames />
        </div>
      </div>
    </div>
  );
};

OverviewTab.propTypes = {
  isUpdate: PropTypes.bool,
  setIsUpdate: PropTypes.func,
};

export default memo(OverviewTab);
