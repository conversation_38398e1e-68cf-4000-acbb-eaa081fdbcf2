import use360requestStore from "store/360requestStore";
import AiAnalysis from "../components/Ai-Analysis";
import ContentDistribution from "../components/ContentDistribution";
import ContentNumber from "../components/ContentNumber";
import Excitement from "../components/Excitement";
import Info from "../components/Info";
import ReleaseChartContainer from "../components/Release";
import RepeatHashtags from "../components/RepeatHashtags";
// import RepeatMentions from "../components/RepeatMentions";
import RepeatNames from "../components/RepeatNames";
import RepeatWords from "../components/RepeatWords";
import Sentiment from "../components/Sentiment";
import ThematicCategory from "../components/ThematicCategory";
import SimilarSources from "../components/SimilarSources";
import ThoughtLine from "../components/ThoughtLine";
import PoliticalSpectrum from "../components/Political";
import { memo } from "react";

const OverviewTab = () => {
  const sourceReport = use360requestStore((state) => ({
    platform: state.report?.content?.report_platform,
  }));
  const platform = sourceReport.platform;

  return platform === "telegram" ? (
    <div className="flex flex-col gap-3 h-full">
      <div className="flex gap-3 h-full">
        <div className="w-[60%] flex flex-col gap-3">
          <div className="flex-none">
            <Info />
          </div>
          <div className="flex flex-col gap-3 flex-1">
            <div className="flex-1">
              <ReleaseChartContainer />
            </div>
            <div className="flex gap-3 flex-1">
              <div className="w-1/2 flex">
                <div className="flex-1">
                  <Sentiment />
                </div>
              </div>
              <div className="w-1/2 flex">
                <div className="flex-1">
                  <Excitement />
                </div>
              </div>
            </div>
            <div className="flex-1 !h-full">
              <RepeatNames />
            </div>
          </div>
        </div>
        <div className="w-[40%] flex flex-col gap-3">
          <div className="flex-1">
            <AiAnalysis />
          </div>
          <div className="flex-1">
            <ContentNumber />
          </div>
          <div>{platform !== "news" && <ContentDistribution />}</div>
          {/* <div>
            <RepeatWords />
          </div> */}
          <div className="flex-1">
            {platform !== "news" && <RepeatHashtags />}
          </div>
        </div>
      </div>
      <div className="flex-1">
        <ThematicCategory />
      </div>
    </div>
  ) : platform === "news" ? (
    <div className="flex flex-col gap-3">
      <div className="flex gap-3 flex-row w-full">
        <div className="w-3/5">
          <Info />
        </div>
        <div className="w-2/5">
          <RepeatNames />
        </div>
      </div>
      <div className="flex gap-3">
        <div className="w-[60%] flex flex-col gap-3">
          <div className="flex-1 flex flex-col">
            <SimilarSources />
          </div>
        </div>

        <div className="w-[40%] flex flex-col gap-3">
          <div className="flex-1 flex flex-col">
            <ThoughtLine />
          </div>
          <div className="flex-1 flex flex-col">
            <PoliticalSpectrum />
          </div>
        </div>
      </div>
      <ReleaseChartContainer isNews={true} />

      <div className="flex gap-3 flex-row w-full">
        <div className="w-3/5">
          <ThematicCategory />
        </div>
        <div className="w-2/5">
          <RepeatWords />
        </div>
      </div>
    </div>
  ) : (
    <div className="flex gap-3 h-full">
      <div className="w-[60%] flex flex-col gap-3">
        <div className="flex-none">
          <Info />
        </div>
        <div className="flex flex-col gap-3 flex-1">
          <div className="flex-1">
            <ReleaseChartContainer />
          </div>
          <div className="flex gap-3 flex-1">
            <div className="w-1/2 flex">
              <div className="flex-1">
                <Sentiment />
              </div>
            </div>
            <div className="w-1/2 flex">
              <div className="flex-1">
                <Excitement />
              </div>
            </div>
          </div>
          <div className="flex-1">
            <ThematicCategory />
          </div>
          {/* <div className="flex-1">
            {!(platform === "news" || platform === "telegram") && (
              <RepeatMentions />
            )}
          </div> */}
        </div>
      </div>
      <div className="w-[40%] flex flex-col gap-3">
        <div className="flex-1">
          <AiAnalysis />
        </div>
        <div className="flex-1">
          <ContentNumber />
        </div>
        {/* <div className="flex-1">
          {platform !== "news" && <ContentDistribution />}
        </div> */}
        {/* <div className="flex-1">
          <RepeatWords isUpdate={isUpdate} />
        </div> */}
        <div className="flex-1">
          <RepeatHashtags />
        </div>
        <div className="flex-1">
          <RepeatNames />
        </div>
      </div>
    </div>
  );
};

export default memo(OverviewTab);
