import { useContext } from "react";
import { Route, Routes, Navigate } from "react-router-dom";

// import Highcharts from "highcharts";
// import HighchartsExporting from "highcharts/modules/exporting";
// import HighchartsExportData from "highcharts/modules/export-data";
// import HighchartsOfflineExporting from "highcharts/modules/offline-exporting";
//
// // Initialize modules
// HighchartsExporting(Highcharts);
// HighchartsExportData(Highcharts);
// HighchartsOfflineExporting(Highcharts);
//
// // Customize export menu text
// Highcharts.setOptions({
//   lang: {
//     downloadPNG: "دانلود تصویر",
//   },
// });

import Loading from "./components/ui/Loading";

// Contexts
import AuthContext from "./context/auth-context";

// Layout
import DashboardLayout from "./layout/dashboard-layout.jsx";
import RouteGuard from "./route-guard";

// User Pages
import Dashboard from "./pages/user/dashboard";
import AdvancedSearch from "./pages/user/advanced-search";
import HotTopic from "./pages/user/hot-topic/index.jsx";

// Public Pages
import Login from "./pages/login";
import NotFound from "./pages/404";
import Error500 from "./pages/500";
import ForgotPassword from "./pages/forgot-password";

import "./App.css";
import AlertList from "./pages/user/alert-list/index.jsx";
import AlertCreate from "./pages/user/alert-create/index.jsx";
import AlertId from "./pages/user/alert-id/index.jsx";
import ShowProfile from "./pages/user/show-profile/index.jsx";
import FilterList from "./pages/user/filter-list/index.jsx";
import FilterCreate from "./pages/user/filter-create/index.jsx";
import FilterId from "./pages/user/filter-id/index.jsx";
import SecurityProfile from "./pages/user/security-profile/index.jsx";
import HotWord from "./pages/user/hot-word/index.jsx";
import Notif from "./pages/user/notif/index.jsx";
import Notifications from "./pages/user/notif/components/Notifications.jsx";
import Announcement from "./pages/user/notif/components/Announcement.jsx";
import NotifDetail from "./pages/user/notif/components/NotifDetail.jsx";
import BulletinList from "./pages/user/bulletin-list/index.jsx";
import BulletinCreate from "./pages/user/bulletin-create/index.jsx";
import Notes from "./pages/user/notes/index.jsx";
import Bookmarks from "./pages/user/bookmarks/index.jsx";
import PhoneNoChange from "./pages/user/phoneNo-change/index.jsx";
import EmailChange from "./pages/user/email-change/index.jsx";
import BulletinId from "./pages/user/bulletin-id/index.jsx";
import CompareList from "./pages/user/compare-list/index.jsx";
import CompareCreate from "./pages/user/compare-create";
import BulletinEdit from "./pages/user/bulletin-edit/index.jsx";
import TicketList from "./pages/user/ticket-list/index.jsx";
import TicketCreate from "./pages/user/ticket-create/index.jsx";
import TicketDetails from "./pages/user/ticket-id/index.jsx";
import Kiosk from "./pages/user/kiosk/index.jsx";
import NewsPaperDetail from "./pages/user/kiosk/components/newsPaperDetail.jsx";
import SpecialNewspaper from "./pages/user/kiosk/components/special-newspaper.jsx";
import SearchResults from "./pages/user/kiosk/components/searchResults.jsx";
import CompareEdit from "pages/user/compare-edit/index.jsx";
import Report360List from "pages/user/360-list/index.jsx";
import AdminDashboard from "pages/admin/dashboard/index.jsx";
import AdminLayout from "layout/admin-layout.jsx";
import AdminUserList from "pages/admin/user-list/index.jsx";
import AdminTicketList from "./pages/admin/ticket-list/index.jsx";
import AdminTicketCreate from "./pages/admin/ticket-create/index.jsx";
import AdminTicketDetails from "./pages/admin/ticket-id/index.jsx";
import AdminUserCreate from "pages/admin/user-create/index.jsx";
import AnnouncementManagement from "./pages/admin/announcement-management/index.jsx";
import AdminUserDetails from "pages/admin/user-id";
import UserProfileSettings from "pages/admin/user-id/components/user-profile-settings";
import UserPlanSettings from "pages/admin/user-id/components/user-plan-settings";
import UserPlanHistoryPage from "pages/admin/user-id/components/user-plan-history";
import UserPlanLoginHistory from "pages/admin/user-id/user-plan-login-history";
import CreateBatchUser from "pages/admin/user-create/batch/create-batch-user";
import UserWallet from "pages/admin/user-id/user-plan-wallet";
import Report360Edit from "pages/user/360-edit";
import WaveAnalysis from "pages/user/wave-analysis";
import WaveAnalysisId from "pages/user/wave-analysis-id";
import WaveAnalysisThoughts from "pages/user/wave-analysis-thoughts";
import OpinionMining from "pages/user/opinion-mining";
import OpinionMiningCreate from "pages/user/opinion-mining/OpinionMiningCreate";
import FilterAccess from "pages/user/filter-access";
import CompareAccess from "pages/user/compare-access";
import Report360Access from "pages/user/360-access";
import RelationDetect from "pages/user/relationDetect";
import AnnouncementList from "pages/admin/announcement-list";
import AdminAlertList from "pages/admin/alert-list/index.jsx";
import AdminFilterList from "pages/admin/filter-list/index.jsx";
import AdminCompareList from "pages/admin/compare-list/index.jsx";
import AnnouncementCreate from "pages/admin/announcement-list/components/announcementCreate";
import Admin360List from "pages/admin/360-list/index.jsx";
import AdminSearchList from "pages/admin/search-list/index.jsx";
import GroupsList from "pages/admin/groups-list";
import GroupsCreate from "pages/admin/groups-list/components/GroupsCreate";
import GroupsDetail from "pages/admin/groups-detail";
import UserGroupAdder from "pages/admin/groups-detail/components/UserGroupAdder";
import GeneratedGraphResults from "pages/user/relationDetect/components/GeneratedGraphResults";
import RelationDetectList from "pages/user/relation-detect-list";
import OpinionMiningEdit from "pages/user/opinion-mining/OpinionMiningEdit";
import DashboardSetting from "pages/user/dashboard-setting/index.jsx";
import Report360Source from "./pages/user/360-report-source/index.jsx";
import Report360CreateStep1 from "./pages/user/360-create/step-1/index.jsx";
import Report360CreateStep2 from "./pages/user/360-create/step-2/index.jsx";

// Admin

function App() {
  const { isLoading, profile } = useContext(AuthContext);

  const redirectionUrls = {
    USER: "/app/dashboard/",
  };

  return isLoading ? (
    <div
      className={"flex flex-row size-full h-screen justify-center align-middle"}
    >
      <Loading className={"m-auto"} />
    </div>
  ) : (
    <Routes>
      <Route
        path="/"
        element={<Navigate replace to={redirectionUrls["USER"] || "/login"} />}
      />

      <Route
        path="/app"
        element={
          <RouteGuard checkWelcome={false} Component={DashboardLayout} />
        }
      >
        <Route index element={<Navigate replace to="/app/dashboard" />} />
        <Route path="dashboard" element={<Dashboard />} />
        <Route path="advanced-search" element={<AdvancedSearch />} />
        <Route path="hot-topic" element={<HotTopic />} />
        <Route path="notes" element={<Notes />} />
        <Route path="bookmarks" element={<Bookmarks />} />
        <Route path="hot-word" element={<HotWord />} />

        <Route path="alert">
          <Route index element={<Navigate replace to="/app/alert/list" />} />
          <Route path="list" element={<AlertList />} />
          <Route path="list/:id" element={<AlertId />} />
          <Route path="create" element={<AlertCreate />} />
          <Route path="edit" element={<AlertCreate />} />
        </Route>

        <Route path="user">
          <Route index element={<Navigate replace to="/app/user/profile" />} />
          <Route path="profile" element={<ShowProfile />} />
          <Route path="profile/phoneNo-change" element={<PhoneNoChange />} />
          <Route path="profile/email-change" element={<EmailChange />} />
          <Route path="security" element={<SecurityProfile />} />
        </Route>

        <Route path="filter">
          <Route index element={<Navigate replace to="/app/filter/list" />} />
          <Route path="list" element={<FilterList />} />
          <Route path="list/:id" element={<FilterId />} />
          <Route path="create" element={<FilterCreate />} />
          <Route path="edit" element={<FilterCreate />} />
          <Route path="access/list/:id" element={<FilterAccess />} />
        </Route>

        <Route path="compare">
          <Route index element={<Navigate replace to="/app/compare/list" />} />
          <Route path="list" element={<CompareList />} />
          <Route path="list/:id" element={<FilterId />} />
          <Route path="create" element={<CompareCreate />} />
          <Route path="edit/:id" element={<CompareEdit />} />
          <Route path="access/list/:id" element={<CompareAccess />} />
        </Route>

        <Route path="bulletin">
          <Route index element={<Navigate replace to="/app/bulletin/list" />} />
          <Route path="list" element={<BulletinList />} />
          <Route path="list/:id" element={<BulletinId />} />
          <Route path="create" element={<BulletinCreate />} />
          <Route path="edit/:id" element={<BulletinEdit />} />
        </Route>

        <Route path="report-360">
          <Route
            index
            element={<Navigate replace to="/app/report-360/list" />}
          />
          <Route path="list" element={<Report360List />} />
          <Route path="create/step-1" element={<Report360CreateStep1 />} />
          <Route path="create/step-2" element={<Report360CreateStep2 />} />
          <Route
            path="report/source/:platform/:id/"
            element={<Report360Source />}
          />
          {/* <Route path="report/entity/:id/" element={<Report360Entity />} /> */}
          {/* <Route path="report/location/:id/" element={<Report360Location />} /> */}
          <Route path="edit" element={<Report360Edit />} />
          <Route path="access/list/:id" element={<Report360Access />} />
        </Route>

        <Route path="notif" element={<Notif />}>
          <Route path="Notifications" element={<Notifications />} />
          <Route path="Announcement" element={<Announcement />} />
          <Route path="Notifications/:id" element={<NotifDetail />} />
          <Route
            index
            element={<Navigate replace to="/app/notif/Notifications" />}
          />
        </Route>
        <Route path="newspaper">
          <Route
            index
            element={<Navigate replace to="/app/newspaper/list" />}
          />
          <Route path="list" element={<Kiosk />} />
          <Route path="list/:id" element={<NewsPaperDetail />} />
          <Route
            path="special-newspaper/:id/:agency"
            element={<SpecialNewspaper />}
          />
          <Route path="newspaper-results" element={<SearchResults />} />
          <Route
            path="newspaper-results/:category"
            element={<SearchResults />}
          />
        </Route>

        <Route path="wave-analysis">
          <Route
            index
            element={<Navigate replace to="/app/wave-analysis/list" />}
          />
          <Route path="list" element={<WaveAnalysis />} />
          {/* <Route path="list/thoughts/:id" element={<WaveAnalysisThoughts />} /> */}
          <Route path="list/:id" element={<WaveAnalysisId />} />
        </Route>

        <Route path="opinion-mining">
          <Route
            index
            element={<Navigate replace to="/app/opinion-mining/list" />}
          />
          <Route path="list" element={<OpinionMining />} />
          <Route path="create" element={<OpinionMiningCreate />} />
          <Route path="edit" element={<OpinionMiningEdit />} />
          <Route path="list/thoughts/:id" element={<WaveAnalysisThoughts />} />
          {/* <Route path="list/:id" element={<WaveAnalysisId />} />
          <Route path="list/thoughts/:id" element={<WaveAnalysisThoughts />} /> */}
        </Route>

        <Route path="relation-detect">
          <Route
            index
            element={<Navigate replace to="/app/relation-detect/list" />}
          />
          <Route path="list" element={<RelationDetectList />} />
          <Route path="create" element={<RelationDetect />} />
          <Route path="results" element={<GeneratedGraphResults />} />
          {/* <Route path="create" element={<OpinionMiningCreate />} /> */}
          {/* <Route path="list/:id" element={<WaveAnalysisId />} />
          <Route path="list/thoughts/:id" element={<WaveAnalysisThoughts />} /> */}
        </Route>

        <Route path="ticket">
          <Route index element={<Navigate replace to="/app/ticket/list" />} />
          <Route path="list" element={<TicketList />} />
          <Route path="create" element={<TicketCreate />} />
          <Route path="list/:id" element={<TicketDetails />} />
        </Route>

        <Route path="dashboard-setting">
          <Route
            index
            element={<Navigate replace to="/app/dashboard-setting/form" />}
          />
          <Route path="form" element={<DashboardSetting />} />
        </Route>
      </Route>

      <Route
        path="/admin"
        element={<RouteGuard role={"Manager"} Component={AdminLayout} />}
      >
        <Route index element={<Navigate replace to="/admin/dashboard" />} />
        <Route path="dashboard" element={<AdminDashboard />} />
        <Route
          path="announcement-management"
          element={<AnnouncementManagement />}
        />
        <Route path="announcement-list" element={<AnnouncementList />} />
        <Route path="announcement/create" element={<AnnouncementCreate />} />
        <Route path="groups-list" element={<GroupsList />} />
        <Route path="groups/create" element={<GroupsCreate />} />
        <Route path="groups/detail" element={<GroupsDetail />} />
        <Route path="groups/detail/add-user" element={<UserGroupAdder />} />

        <Route path="ticket">
          <Route index element={<Navigate replace to="/admin/ticket/list" />} />
          <Route path="list" element={<AdminTicketList />} />
          <Route path="create" element={<AdminTicketCreate />} />
          <Route path="list/:id" element={<AdminTicketDetails />} />
        </Route>

        <Route path="alerts">
          <Route index element={<Navigate replace to="/admin/alerts/list" />} />
          <Route path="list" element={<AdminAlertList />} />
        </Route>

        <Route path="filters">
          <Route
            index
            element={<Navigate replace to="/admin/filters/list" />}
          />
          <Route path="list" element={<AdminFilterList />} />
        </Route>

        <Route path="compare">
          <Route
            index
            element={<Navigate replace to="/admin/compare/list" />}
          />
          <Route path="list" element={<AdminCompareList />} />
        </Route>

        <Route path="360-report">
          <Route
            index
            element={<Navigate replace to="/admin/360-report/list" />}
          />
          <Route path="list" element={<Admin360List />} />
        </Route>

        <Route path="search">
          <Route index element={<Navigate replace to="/admin/search/list" />} />
          <Route path="list" element={<AdminSearchList />} />
        </Route>

        <Route path="management-reports"></Route>
        <Route path="bulletin"></Route>
        <Route path="kiosk"></Route>

        <Route path="user">
          <Route index element={<Navigate replace to="/admin/user/list" />} />
          <Route path="createUser" element={<AdminUserCreate />} />
          <Route path="createBatchUser" element={<CreateBatchUser />} />
          <Route path="list" element={<AdminUserList />} />
          <Route path="list/:id" element={<AdminUserDetails />} />
          <Route path="list/:id/settings" element={<UserProfileSettings />} />
          <Route path="list/:id/planSettings" element={<UserPlanSettings />} />
          <Route
            path="list/:id/planHistory"
            element={<UserPlanHistoryPage />}
          />
          <Route
            path="list/:id/planLoginHistory"
            element={<UserPlanLoginHistory />}
          />
          <Route path="list/:id/planWallet" element={<UserWallet />} />
        </Route>
      </Route>

      <Route
        path="/login"
        element={
          profile ? (
            <Navigate replace to={redirectionUrls["USER"]} />
          ) : (
            <Login />
          )
        }
      />
      <Route
        path="/forgot-password"
        element={
          profile ? (
            <Navigate replace to={redirectionUrls["USER"]} />
          ) : (
            <ForgotPassword />
          )
        }
      />

      <Route path="/500" element={<Error500 />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default App;
