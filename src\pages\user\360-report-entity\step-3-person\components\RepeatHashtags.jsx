import { memo, useEffect, useState } from "react";
import HorizontalBar from "components/Charts/HorizontalBar";
import { Cloud, FunnelSimple } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ReactWordcloud from "react-wordcloud";
import PropTypes from "prop-types";
import CLUSTER_COLORS from "constants/colors.js";
import "../../style.css";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import { Tabs360 } from "./360tabs";
import { preprocessWord, toPersianNumber } from "utils/helper";
import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";
import { useReport360Store } from "store/report360Store";
import use360requestStore from "store/360requestStore";
import usePlatformDataStore from "store/person360platform";
import ExportMenu from "components/ExportMenu/index.jsx";
import Drawer from "components/Drawer";
import WordContent from "pages/user/word-content";

const PersonRepeatHashtags = ({ activePlatform, isUpdate }) => {
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("wordCloud");
  const [words, setWords] = useState([]);
  const [info, setInfo] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showWord, setShowWord] = useState(false);
  const [word, setWord] = useState("");
  const [isDateChange, setIsDateChange] = useState(false); // Track date changes
  const { date, entity, type } = useReport360Store((state) => state.report);
  const locationEntityCharts = use360requestStore(
    (state) => state.locationEntityCharts
  );
  const report = use360requestStore((state) => state.report);
  const reportType =
    type === "topic" ? "entity" : type === "location" ? "location" : null;
  const chartData = report[reportType]?.[activePlatform]?.frequent_hashtags;

  // Access Zustand store
  const { platformData, setPlatformData, clearPlatformData } =
    usePlatformDataStore((state) => ({
      platformData: state.platformData[activePlatform]?.repeatHashtags,
      setPlatformData: state.setPlatformData,
      clearPlatformData: state.clearPlatformData,
    }));

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Hashtag Count",
      data: words.map((item) => item.value),
      time: words.map((item) => item.text),
    },
  ];

  const time = words.map((item) => item.text);

  const getData = async (abortController) => {
    // Use cached data if available, not updating, and date hasn't changed
    if (
      !isUpdate &&
      !isDateChange &&
      platformData &&
      Array.isArray(platformData) &&
      platformData.length > 0
    ) {
      const transformedWords = platformData.map(({ key, count }) => ({
        text: preprocessWord(key),
        value: count,
      }));
      setWords(transformedWords);
      setInfo(
        platformData.map(({ key, count }) => ({
          word: preprocessWord(key),
          count,
        }))
      );
      setCategories(platformData.map(({ key }) => preprocessWord(key)));
      setLoading(false);
      return;
    }

    // Use chartData if updating, data exists, and date hasn't changed
    if (
      isUpdate &&
      !isDateChange &&
      chartData &&
      Array.isArray(chartData) &&
      chartData.length > 0
    ) {
      const transformedWords = chartData.map(({ key, count }) => ({
        text: preprocessWord(key),
        value: count,
      }));
      setWords(transformedWords);
      setInfo(
        chartData.map(({ key, count }) => ({
          word: preprocessWord(key),
          count,
        }))
      );
      setCategories(chartData.map(({ key }) => preprocessWord(key)));
      setPlatformData(activePlatform, "repeatHashtags", chartData); // Cache data
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          date,
          q: entity,
          platform: activePlatform,
        },
        "cloud",
        20
      );

      const res = await advanceSearch.search(
        requestData,
        abortController.signal,
        {
          cloud_type: "hashtags",
        }
      );

      const platformData = res?.data?.data?.[activePlatform] || [];

      const transformedWords = platformData.map(({ key, count }) => ({
        text: preprocessWord(key),
        value: count,
      }));

      const convertedWords = platformData.map(({ key, count }) => ({
        key: preprocessWord(key),
        count: count,
      }));

      setWords(transformedWords);
      setInfo(
        platformData.map(({ key, count }) => ({
          word: preprocessWord(key),
          count,
        }))
      );
      setCategories(platformData.map(({ key }) => preprocessWord(key)));
      setPlatformData(activePlatform, "repeatHashtags", platformData); // Cache data

      locationEntityCharts(
        convertedWords,
        "frequent_hashtags",
        activePlatform,
        reportType
      );
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error(error);
        setWords([]);
        setInfo([]);
        setCategories([]);
      }
    } finally {
      if (!abortController.signal.aborted) {
        setLoading(false);
        setIsDateChange(false); // Reset date change flag after fetching
      }
    }
  };

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  useEffect(() => {
    const abortController = new AbortController();
    if (isUpdate) {
      clearPlatformData(activePlatform, "repeatHashtags"); // Clear only repeatHashtags cache
    }
    getData(abortController);
    return () => abortController.abort();
  }, [entity, date, activePlatform, isUpdate]);

  // Detect date changes and set isDateChange to true
  useEffect(() => {
    setIsDateChange(true);
  }, [date]);

  const getGreenColor = (count) => {
    const maxCount = Math.max(...info.map((item) => item.count));
    const minCount = Math.min(...info.map((item) => item.count));

    const intensity = Math.floor(
      ((maxCount - count) / (maxCount - minCount)) * 155
    );

    return `rgb(${30 + intensity}, 200, ${30 + intensity})`;
  };

  const handleWordClick = (clickedWord) => {
    setWord(clickedWord.text);
    setShowWord(true);
  };

  return (
    <div className="flex">
      <Card className="px-0 card-animation card-delay !h-[31rem]">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row gap-2 w-full justify-between">
            <div className="w-1/2 flex items-center">
              <Title title="هشتگ‌های پر تکرار" />
              <ExportMenu
                chartSelector=".person-repeat-hashtags-container"
                fileName="person-repeat-hashtags"
                series={series}
                time={time}
                excelHeaders={["Hashtag", "Count"]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
                chartTitle="هشتگ‌های پر تکرار"
              />
            </div>
            <div className="flex items-center gap-4 w-1/2">
              <Tabs360
                tabArray={[
                  { id: "wordCloud", title: "ابر کلمات", icon: Cloud },
                  { id: "cluster", title: "نمودار", icon: FunnelSimple },
                ]}
                activeTab={wordCloudActiveTab}
                onChange={onClusterTabChange}
              />
            </div>
          </div>
          <div className="grid person-repeat-hashtags-container grid-cols-1 h-full">
            {loading ? (
              <div className="h-full flex items-center justify-center font-subtitle-medium">
                در حال بارگذاری...
              </div>
            ) : info?.length ? (
              <>
                {wordCloudActiveTab === "wordCloud" && (
                  <div dir="ltr" className="flex flex-1 responsive-svg h-full">
                    <ReactWordcloud
                      options={{
                        rotations: 1,
                        rotationAngles: [0],
                        enableTooltip: true,
                        deterministic: false,
                        fontFamily: "iranyekan",
                        fontSizes: [14, 54],
                        padding: 10,
                        colors: CLUSTER_COLORS,
                        tooltipOptions: { theme: "light", arrow: true },
                      }}
                      words={words}
                      callbacks={{
                        getWordTooltip: (word) => {
                          const countInPersian = toPersianNumber(word.value);
                          return `${word.text} (${countInPersian})`;
                        },
                        onWordClick: handleWordClick,
                      }}
                    />
                  </div>
                )}
                {wordCloudActiveTab === "cluster" && (
                  <div className="flex justify-end h-full w-full">
                    <HorizontalBar
                      colors={info.map((item) => getGreenColor(item.count))}
                      info={info}
                      categories={categories}
                      style={{ height: "100%" }}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className="h-full flex items-center justify-center font-subtitle-medium">
                داده ای برای نمایش وجود ندارد
              </div>
            )}
          </div>
        </div>
      </Card>
      {showWord && (
        <Drawer setShowMore={setShowWord}>
          <WordContent word={word} />
        </Drawer>
      )}
    </div>
  );
};

PersonRepeatHashtags.propTypes = {
  activePlatform: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool,
};

export default memo(PersonRepeatHashtags);
