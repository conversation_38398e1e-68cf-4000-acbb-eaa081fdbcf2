import { useBreadcrumb } from "hooks/useBreadcrumb";
import GraphPreview from "./components/GraphSettings/components/GraphPreview";
import { CButton } from "components/ui/CButton";
import { Info, ListBullets } from "@phosphor-icons/react";
import EdgeSettings from "./components/GraphSettings/components/EdgeSettings";
import { useState } from "react";
import { Link } from "react-router-dom";

const RelationDetect = () => {
  const breadcrumbList = [
    { title: "شناسایی روابط", link: "/app/relation-detect/list" },
    { title: "پیش نمایش" },
  ];
  useBreadcrumb(breadcrumbList);
  const [displayBadges, setDisplayBadges] = useState(false);

  return (
    <div className="grid grid-cols-12 gap-4 mx-auto px-5 max-h-screen ">
      <div className="col-span-12 flex justify-end gap-2">
        <Link to="/app/relation-detect/list" className="w-fit">
          <CButton mode="outline" size="sm" className="gap-2 !h-10">
            <ListBullets size={17} className="text-light-primary-text-rest" />
            <p className="text-light-primary-text-rest font-button-medium">
              گراف‌های ذخیره شده
            </p>
          </CButton>
        </Link>
        <div className="w-fit">
          <CButton
            mode="outline"
            size="sm"
            role="neutral"
            className="gap-2 !h-10"
          >
            <Info size={17} className="text-light-neutral-text-medium" />
            <p className="text-light-neutral-text-medium font-button-medium">
              راهنمای شناسایی روابط
            </p>
          </CButton>
        </div>
      </div>
      <div className="col-span-6 overflow-y-scroll max-h-screen no-scrollbar -mx-1">
        <EdgeSettings setDisplayBadges={setDisplayBadges} />
      </div>
      <div className="col-span-6">
        <GraphPreview
          displayBadges={displayBadges}
          setDisplayBadges={setDisplayBadges}
        />
      </div>
    </div>
  );
};

export default RelationDetect;
