import { Card } from "components/ui/Card";
import source360 from "../../../../assets/images/360/source360.png";
import location360 from "../../../../assets/images/360/location360.png";
import entity360 from "../../../../assets/images/360/entity360.png";
import Divider from "components/ui/Divider";
import {
  Eye,
  Faders,
  PencilSimpleLine,
  TrashSimple,
  X,
} from "@phosphor-icons/react";
import { useEffect, useRef, useState } from "react";
import report360 from "service/api/report360";
import { notification, parseTimeToPersian } from "utils/helper";
import { Link, useNavigate } from "react-router-dom";
import user from "../../../../assets/images/default.png";
import Popup from "./popUp";
import { CInput } from "components/ui/CInput";
import DeletePopUp from "components/ui/DeletePopUp.jsx";
import MediaBadge from "components/ui/MediaBadge.jsx";

const Card360 = ({ onData, order, reportType, searchQuery }) => {
  const [data, setData] = useState([]);
  const [groupData, setGroupData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [showRemove, setShowRemove] = useState(null);
  const [showEdit, setShowEdit] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const navigate = useNavigate();
  const fileInputRef = useRef(null);

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    const maxSizeInBytes = 1 * 1024 * 1024; // 1MB

    if (file) {
      if (file.size > maxSizeInBytes) {
        notification.error(
          "حجم فایل انتخاب شده نباید بیشتر از ۱ مگابایت باشد."
        );
        return;
      }

      const imageUrl = URL.createObjectURL(file);
      setSelectedImage({ file, url: imageUrl });
    }
  };

  const saveEditedReport = async () => {
    if (!showEdit) return;

    setLoading(true);
    try {
      let avatarUrl = showEdit.avatar;

      if (selectedImage?.file) {
        const formData = new FormData();
        formData.append("image", selectedImage.file);

        const uploadedImageRes = await report360.image(formData);
        avatarUrl = uploadedImageRes.data.data.image;
      }

      const reportState = {
        id: showEdit.id,
        title: showEdit.title,
        avatar: avatarUrl,
      };

      await report360.update(showEdit.id, reportState);

      setShowEdit(null);
      setSelectedImage(null);
      getData(page);
    } catch (error) {
      console.log(error);
      notification.error("خطایی در ذخیره گزارش رخ داد.");
    }
    setLoading(false);
  };

  const translateType = (type) => {
    switch (type) {
      case "source":
        return "منبع";
      case "entity":
        return "موجودیت";
      case "location":
        return "مکان";
      default:
        return type;
    }
  };

  const getTypeImage = (report_type) => {
    switch (report_type) {
      case "source":
        return source360;
      case "entity":
        return entity360;
      case "location":
        return location360;
      default:
        return source360;
    }
  };

  const getPostCount = (contentCount) => {
    if (!contentCount || !Array.isArray(contentCount)) return "0";
    const postItem = contentCount.find((item) => item.key === "post");
    return postItem ? postItem.count.toLocaleString() : "0";
  };

  const getData = async (page) => {
    setLoading(true);
    try {
      const reportTypeString =
        reportType.length > 0 ? reportType.join(",") : "source,entity,location";
      const response = await report360.get({
        order,
        report_type: reportTypeString,
      });
      const translatedData = response.data?.data?.user?.map((item) => ({
        ...item,
        type: translateType(item.report_type),
      }));
      const translatedGroupData = response.data?.data?.group?.map((item) => ({
        ...item,
        type: translateType(item.report_type),
        sharedOne: true,
      }));
      onData(translatedData);
      setData(translatedData);
      setGroupData(translatedGroupData);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const remove360 = async (id) => {
    setLoading(true);
    try {
      await report360.remove(id);
      setData((prevData) => prevData.filter((item) => item.id !== id));
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    getData(page);
  }, [page, order, reportType]);

  const parseDateToPersian = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString("fa-IR", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  };

  const filteredData = searchQuery
    ? [...data, ...groupData].filter((item) => {
        const words = item.title.toLowerCase().split(/\s+/);
        const firstFewWords = words.slice(0, 3);
        return firstFewWords.some((word) =>
          word.startsWith(searchQuery.toLowerCase())
        );
      })
    : [...data, ...groupData];

  return (
    <>
      {data.length === 0 && groupData.length === 0 && (
        <div className="w-full h-[640px] flex flex-col justify-center items-center">
          <div className="size-12 bg-[url(/empty_folder.png)] bg-no-repeat bg-contain bg-center mb-4"></div>
          <p className="font-body-large mb-2">هنوز گزارشی ساخته نشده است</p>
          <p className="font-body-medium">
            لطفا بر روی دکمه
            <Link
              to="/app/report-360/create"
              className="underline underline-offset-4 text-light-primary-text-rest cursor-pointer"
            >
              {" "}
              گزارش جدید{" "}
            </Link>
            کلیک کنید
          </p>
        </div>
      )}
      <div className="grid grid-cols-4 gap-6 md:grid-cols-5">
        {filteredData.map(
          ({
            id,
            title,
            avatar,
            type,
            start_date,
            end_date,
            created_at,
            report_type,
            identity,
            platform,
            content_count,
          }) => (
            <Card key={id} className="!w-[100%] flex flex-col justify-center">
              <div className="flex flex-col justify-center">
                <div className="flex justify-center pb-1 relative">
                  <div className="relative">
                    <img
                      src={avatar !== null ? avatar : getTypeImage(report_type)}
                      className="!w-20 !h-20 object-cover rounded-full"
                      onError={(e) => {
                        e.target.onError = null;
                        e.target.src = user;
                      }}
                    />
                    {report_type === "source" && (
                      <span className="absolute bottom-0 right-0 rounded-full overflow-hidden !w-[28px] !h-[28px] bg-white">
                        <MediaBadge
                          media={platform !== "" ? platform : ""}
                          className="!h-[28px] !w-[28px]"
                        />
                      </span>
                    )}
                  </div>
                </div>
                <div className="font-subtitle-large w-full truncate text-center">
                  {title}
                </div>
                <span className="font-overline-medium flex justify-center text-[#878787]">
                  {type}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <div className="flex flex-col gap-2">
                  <span className="font-overline-medium text-[#878787]">
                    بازه زمانی
                  </span>
                  <span className="font-overline-medium text-[#878787]">
                    تاریخ ایجاد
                  </span>
                </div>
                <div className="flex flex-col gap-2 text-left">
                  <span className="font-overline-medium">
                    {`${parseDateToPersian(start_date)} - ${parseDateToPersian(
                      end_date
                    )}`}
                  </span>
                  <span className="font-overline-medium">
                    {created_at
                      ? parseTimeToPersian(created_at).split("-")[1]
                      : ""}
                  </span>
                </div>
              </div>
              <Divider />
              <div className="flex justify-center items-center pt-4 gap-6">
                <div
                  className="p-1 rounded-md bg-[#eff3f6] hover:cursor-pointer"
                  onClick={() =>
                    navigate(`/app/report-360/edit`, { state: { id } })
                  }
                >
                  <Eye />
                </div>
                <div
                  className="p-1 rounded-md bg-[#eff3f6] hover:cursor-pointer"
                  onClick={() => navigate(`/app/report-360/access/list/${id}`)}
                >
                  <Faders />
                </div>
                <div
                  className="p-1 rounded-md bg-[#eff3f6] hover:cursor-pointer"
                  onClick={() =>
                    setShowEdit({ id, identity, report_type, title, avatar })
                  }
                >
                  <PencilSimpleLine />
                </div>
                <div
                  className="p-1 rounded-md bg-[#eff3f6] hover:cursor-pointer"
                  onClick={() => setShowRemove(id)}
                >
                  <TrashSimple />
                </div>
              </div>

              <DeletePopUp
                onClose={() => setShowRemove(false)}
                isOpen={showRemove === id}
                submitHandler={() => remove360(id)}
                title="آیا مطمئن هستید؟"
                description="در صورت حذف این گزارش، امکان بازیابی آن وجود ندارد"
              />
            </Card>
          )
        )}
        {showEdit && (
          <Popup isOpen={!!showEdit} onClose={() => setShowEdit(null)}>
            <div className="w-[505px] flex flex-col gap-5">
              <div className="flex w-full justify-between items-center font-subtitle-large">
                <span>ویرایش گزارش</span>
                <X
                  onClick={() => setShowEdit(null)}
                  className="cursor-pointer"
                />
              </div>
              <div
                className="flex justify-center cursor-pointer relative"
                onClick={() => fileInputRef.current?.click()}
              >
                <img
                  src={
                    selectedImage?.url ||
                    data.find((item) => item.id === showEdit.id)?.avatar ||
                    (showEdit?.identity !== null
                      ? showEdit?.identity?.avatar
                      : getTypeImage(showEdit?.report_type))
                  }
                  className="!w-28 !h-28 rounded-full flex justify-center"
                />
                <input
                  type="file"
                  ref={fileInputRef}
                  accept="image/png, image/jpeg"
                  className="hidden"
                  onChange={handleImageChange}
                />
              </div>
              <div className="flex flex-col gap-2">
                <CInput
                  value={showEdit?.title}
                  title="عنوان گزارش"
                  onChange={(e) =>
                    setShowEdit((prev) => ({ ...prev, title: e.target.value }))
                  }
                />
              </div>
              <div className="flex gap-6 w-full font-button-medium">
                <div
                  className="bg-light-neutral-background-medium cursor-pointer rounded-lg w-full h-10 flex items-center justify-center"
                  onClick={() => {
                    setShowEdit(null);
                    setSelectedImage(null);
                  }}
                >
                  انصراف
                </div>
                <div
                  className="text-white bg-[#432fa7] cursor-pointer rounded-lg w-full h-10 flex items-center justify-center"
                  onClick={saveEditedReport}
                >
                  ذخیره
                </div>
              </div>
            </div>
          </Popup>
        )}
      </div>
    </>
  );
};

export default Card360;
