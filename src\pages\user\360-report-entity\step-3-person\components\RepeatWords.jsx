import { useEffect, useState } from "react";
import { Cloud, FunnelSimple } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ReactWordcloud from "react-wordcloud";
import PropTypes from "prop-types";
import CLUSTER_COLORS from "constants/colors.js";
import "../../style.css";
import Title from "../../../compare-create/components/step-two/charts/Title.jsx";
import { Tabs360 } from "./360tabs";
import HorizontalBar from "components/Charts/HorizontalBar";
import { toPersianNumber } from "utils/helper";
import { useReport360Store } from "store/report360Store";
import use360requestStore from "store/360requestStore";
import usePlatformDataStore from "store/person360platform";

const PersonRepeatWords = ({ activePlatform, isUpdate }) => {
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("cluster");
  const [words, setWords] = useState([]);
  const [info, setInfo] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const { date, entity, type } = useReport360Store((state) => state.report);
  const locationEntityCharts = use360requestStore(
    (state) => state.locationEntityCharts
  );
  const report = use360requestStore((state) => state.report);
  const { platformData, setPlatformData } = usePlatformDataStore();
  const reportType =
    type === "topic" ? "entity" : type === "location" ? "location" : null;
  const chartData = report?.[reportType]?.[activePlatform]?.frequent_words;

  // const getData = async (abortController) => {
  //   // Check cache first
  //   const cachedData = platformData[activeSpan]?.frequentWords;
  //   if (cachedData && !isUpdate) {
  //     console.log("Using cached data:", cachedData);
  //     setWords(cachedData.words);
  //     setInfo(cachedData.info);
  //     setCategories(cachedData.categories);
  //     setLoading(false);
  //     return;
  //   }

  //   // Use chartData if isUpdate is true and data exists
  //   if (isUpdate && chartData?.length > 0) {
  //     const transformedWords = chartData.map(({ key, count }) => ({
  //       text: preprocessWord(key),
  //       value: count,
  //     }));
  //     const infoData = chartData.map(({ key, count }) => ({
  //       word: preprocessWord(key),
  //       count,
  //     }));
  //     const categoriesData = chartData.map(({ key }) => preprocessWord(key));

  //     console.log("Using chartData:", {
  //       transformedWords,
  //       infoData,
  //       categoriesData,
  //     });
  //     setWords(transformedWords);
  //     setInfo(infoData);
  //     setCategories(categoriesData);
  //     setPlatformData(activeSpan, "frequentWords", {
  //       words: transformedWords,
  //       info: infoData,
  //       categories: categoriesData,
  //     });
  //     setLoading(false);
  //     return;
  //   }

  //   // Fetch new data
  //   setLoading(true);
  //   console.log("Fetching new data for:", activeSpan);
  //   try {
  //     const requestData = buildRequestData(
  //       {
  //         date,
  //         q: entity,
  //         platform: activeSpan,
  //       },
  //       "cloud"
  //     );
  //     const res = await advanceSearch.search(requestData, abortController);

  //     const platformDataResponse = res?.data?.data?.[activeSpan] || [];

  //     const transformedData = platformDataResponse.map(({ key, count }) => ({
  //       text: preprocessWord(key),
  //       value: count,
  //     }));

  //     const convertedData = platformDataResponse.map(({ key, count }) => ({
  //       key: preprocessWord(key),
  //       count: count,
  //     }));

  //     const infoData = platformDataResponse.map(({ key, count }) => ({
  //       word: preprocessWord(key),
  //       count,
  //     }));

  //     const categoriesData = platformDataResponse.map(({ key }) =>
  //       preprocessWord(key)
  //     );

  //     locationEntityCharts(
  //       convertedData,
  //       "frequent_words",
  //       activeSpan,
  //       reportType
  //     );

  //     console.log("Transformed data:", {
  //       transformedData,
  //       infoData,
  //       categoriesData,
  //     });
  //     setWords(transformedData);
  //     setInfo(infoData);
  //     setCategories(categoriesData);
  //     setPlatformData(activeSpan, "frequentWords", {
  //       words: transformedData,
  //       info: infoData,
  //       categories: categoriesData,
  //     });
  //   } catch (error) {
  //     if (!abortController.signal.aborted) {
  //       console.error("Error fetching data:", error);
  //     }
  //     setWords([]);
  //     setInfo([]);
  //     setCategories([]);
  //   } finally {
  //     if (!abortController.signal.aborted) {
  //       setLoading(false);
  //     }
  //   }
  // };

  useEffect(() => {
    const abortController = new AbortController();
    // getData(abortController);
    return () => abortController.abort();
  }, [date, entity, activePlatform, isUpdate]);

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  const getGreenColor = (count) => {
    const maxCount = Math.max(...info.map((item) => item.count));
    const minCount = Math.min(...info.map((item) => item.count));

    const intensity = Math.floor(
      ((maxCount - count) / (maxCount - minCount)) * 155
    );

    return `rgb(${30 + intensity}, 200, ${30 + intensity})`;
  };

  return (
    <div className="flex !h-full">
      <Card className="px-0 card-animation card-delay !h-[31rem]">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row gap-2 w-full justify-between">
            <div className="w-1/2">
              <Title title="کلمات پر تکرار"></Title>
            </div>
            <div className="flex w-1/2">
              <Tabs360
                tabArray={[
                  { id: "wordCloud", title: "ابر کلمات", icon: Cloud },
                  { id: "cluster", title: "نمودار", icon: FunnelSimple },
                ]}
                activeTab={wordCloudActiveTab}
                onChange={onClusterTabChange}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 !h-full">
            {loading ? (
              <div className="h-full flex items-center justify-center font-subtitle-medium">
                در حال بارگذاری...
              </div>
            ) : info.length > 0 ? (
              <>
                {wordCloudActiveTab === "wordCloud" && (
                  <div className="flex flex-1 responsive-svg h-full">
                    <ReactWordcloud
                      options={{
                        rotations: 1,
                        rotationAngles: [0],
                        enableTooltip: true,
                        deterministic: false,
                        fontFamily: "iranyekan",
                        fontSizes: [14, 54],
                        padding: 10,
                        colors: CLUSTER_COLORS,
                        tooltipOptions: { theme: "light", arrow: true },
                      }}
                      words={words}
                      callbacks={{
                        getWordTooltip: (word) => {
                          const countInPersian = toPersianNumber(word.value);
                          return `${word.text} (${countInPersian})`;
                        },
                      }}
                    />
                  </div>
                )}
                {wordCloudActiveTab === "cluster" && (
                  <HorizontalBar
                    colors={info.map((item) => getGreenColor(item.count))}
                    info={info}
                    categories={categories}
                    style={{ height: "100%" }}
                  />
                )}
              </>
            ) : (
              <div className="h-full flex items-center justify-center font-subtitle-medium">
                داده ای برای نمایش وجود ندارد
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

PersonRepeatWords.propTypes = {
  activePlatform: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool,
};

export default PersonRepeatWords;
