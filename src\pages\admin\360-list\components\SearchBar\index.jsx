import { useState } from "react";
import { CInput } from "components/ui/CInput.jsx";
import { MagnifyingGlass } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton.jsx";
import PropTypes from "prop-types";
import UserDropdown from "./UserDropdown.jsx";
// import StatusDropdown from "./StatusDropdown.jsx";
import SortDropdown from "./SortDropdown.jsx";
import ReportType from "./ReportType.jsx";
import ReportPlatform from "./ReportPlatform.jsx";

const SearchBar = ({ filter, setFilter, loading = false }) => {
  const [searchValue, setSearchValue] = useState(filter.q || "");

  const handleFilterChange = (key, value) => {
    setFilter({ ...filter, [key]: value });
  };

  const handleSearch = async (event) => {
    if (event && event.key === "Enter") {
      await handleSubmit();
    }
  };

  const handleSubmit = async () => {
    setFilter({ ...filter, q: searchValue });
  };

  return (
    <div className="font-body-medium h-full bg-white p-5 overflow-hidden rounded-lg shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]">
      <div className="flex flex-row gap-[16px]">
        <CInput
          id={"q"}
          name={"q"}
          inset={true}
          headingIcon={<MagnifyingGlass />}
          size={"md"}
          validation={"none"}
          inputProps={{ onKeyDown: handleSearch }}
          direction={"rtl"}
          placeholder={"عنوان گزارش را جست‌و‌جو کنید"}
          onChange={(e) => setSearchValue(e.target.value)}
          className={"flex-1 !mb-0"}
        />
        <CButton
          type={"submit"}
          onClick={handleSubmit}
          size={"md"}
          className={"[direction:rtl] [width:150px!important]"}
          disabled={loading}
        >
          جست‌وجو
        </CButton>
      </div>
      <div className="flex pt-1 gap-4">
        <UserDropdown onChange={(value) => handleFilterChange("user", value)} />

        <ReportType onChange={(value) => handleFilterChange("status", value)} />

        <ReportPlatform
          onChange={(value) => handleFilterChange("comparePlatform", value)}
        />

        <SortDropdown onChange={(value) => handleFilterChange("sort", value)} />
      </div>
    </div>
  );
};

SearchBar.propTypes = {
  filter: PropTypes.object.isRequired,
  setFilter: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
};

export default SearchBar;
