// import React, { useCallback, useEffect, useState } from "react";
// import PropTypes from "prop-types";
// import {
//   Calendar,
//   CaretLeft,
//   CaretRight,
//   Check,
//   Eye,
//   EyeClosed,
//   Plus,
//   X,
// } from "@phosphor-icons/react";
// import DatePicker from "react-multi-date-picker";
// import persian from "react-date-object/calendars/persian";
// import persian_fa from "react-date-object/locales/persian_fa";
// import { TagButton } from "./TagButton.jsx";

// export const DateInput = ({
//   id,
//   size = "md",
//   inset = false,
//   state = "rest",
//   validation = "none",
//   direction = "rtl",
//   title,
//   value,
//   link,
//   linkText,
//   successMessage,
//   disabled,
//   className,
//   onChange,
//   field,
//   dateButtonsClassName,
//   inputProps,
//   form: { errors, touched },
// }) => {
//   const [inputClasses, setInputClasses] = useState("");
//   const [fromValue, setFromValue] = useState(new Date(value?.from));
//   const [toValue, setToValue] = useState(new Date(value?.to));
//   const [values, setValues] = useState([fromValue, toValue]);
//   const [selectedOption, setSelectedOption] = useState(value.index || 0);
//   const getInputClasses = useCallback(() => {
//     let baseClass = "c-input";
//     let classes = baseClass;
//     classes += ` ${baseClass}-${size}`;
//     if (inset) classes += ` ${baseClass}-inset`;
//     classes += ` ${baseClass}-${state}`;
//     if (touched[field.name] && errors[field.name])
//       classes += ` ${baseClass}-error`;
//     classes += ` ${baseClass}-${direction}`;
//     classes += ` ${className || ""}`;
//     // return 'cinput cinput-sm cinput-rest cinput-ltr';
//     return classes;
//   }, [
//     className,
//     disabled,
//     state,
//     validation,
//     size,
//     inset,
//     direction,
//     errors,
//     touched,
//   ]);

//   useEffect(() => {
//     setInputClasses(getInputClasses());
//   }, [disabled, getInputClasses, validation, errors, touched, successMessage]);

//   const getDateBefore = (daysOrMonths) => {
//     const currentDate = new Date();
//     if (daysOrMonths === 0) {
//       // Start time of today
//       return new Date(
//         currentDate.getFullYear(),
//         currentDate.getMonth(),
//         currentDate.getDate()
//       );
//     } else {
//       const newDate = new Date(currentDate);
//       if (daysOrMonths > 0) {
//         newDate.setDate(newDate.getDate() - daysOrMonths);
//       } else {
//         newDate.setMonth(newDate.getMonth() - Math.abs(daysOrMonths));
//       }
//       return newDate;
//     }
//   };

//   useEffect(() => {
//     if (selectedOption === -1) return;
//     const fromDate = getDateBefore(selectedOption);
//     setFromValue(fromDate);
//     setToValue(new Date());
//     setValues([fromDate, new Date()]);
//     onChange({
//       from: fromDate,
//       to: new Date(),
//       index: selectedOption,
//     });
//   }, [selectedOption]);

//   const optionIsActive = (index) => {
//     return selectedOption === index;
//   };

//   const handleDateChange = (e) => {
//     if (e.length < 2) return;
//     setFromValue(new Date(e[0]));
//     setToValue(new Date(e[1]));
//     setSelectedOption(-1);
//     onChange({
//       from: new Date(e[0]),
//       to: new Date(e[1]),
//       index: -1,
//     });
//   };

//   return (
//     <div className={inputClasses}>
//       <div className={"label-wrapper"}>
//         {title && <label htmlFor={field.name}>{title}</label>}
//         {link && <a href={link}>{linkText}</a>}
//       </div>
//       <div className={"input-wrapper"}>
//         <DatePicker
//           value={values}
//           calendar={persian}
//           locale={persian_fa}
//           calendarPosition="bottom-right"
//           range
//           containerClassName={"datepicker-container"}
//           onChange={handleDateChange}
//           renderButton={(direction, handleClick) => (
//             <button onClick={handleClick}>
//               {direction === "right" ? (
//                 <CaretLeft size={20} />
//               ) : (
//                 <CaretRight size={20} />
//               )}
//             </button>
//           )}
//         />
//         <span className="action-icon text-left">
//           <Calendar />
//         </span>
//       </div>
//       <div className={"text-right"}>
//         <div
//           className={`flex flex-row justify-between gap-2 my-4 ${dateButtonsClassName}`}
//         >
//           <TagButton
//             isActive={optionIsActive(3)}
//             title={"۳ روز گذشته"}
//             onClick={() => {
//               setSelectedOption(3);
//             }}
//             className={"flex-1"}
//           />
//           <TagButton
//             isActive={optionIsActive(1)}
//             title={"۲۴ ساعت گذشته"}
//             onClick={() => {
//               setSelectedOption(1);
//             }}
//             className={"flex-1"}
//           />
//           <TagButton
//             isActive={optionIsActive(0)}
//             title={"امروز"}
//             onClick={() => {
//               setSelectedOption(0);
//             }}
//             className={"flex-1"}
//           />
//         </div>
//         <div className={"flex flex-row justify-between gap-2 my-4"}>
//           <TagButton
//             isActive={optionIsActive(30)}
//             title={"ماه گذشته"}
//             onClick={() => {
//               setSelectedOption(30);
//             }}
//             className={"flex-1"}
//           />
//           <TagButton
//             isActive={optionIsActive(10)}
//             title={"۱۰ روز گذشته"}
//             onClick={() => {
//               setSelectedOption(10);
//             }}
//             className={"flex-1"}
//           />
//           <TagButton
//             isActive={optionIsActive(7)}
//             title={"هفته گذشته"}
//             onClick={() => {
//               setSelectedOption(7);
//             }}
//             className={"flex-1"}
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// DateInput.propTypes = {
//   id: PropTypes.string,
//   // name: PropTypes.string.isRequired,
//   type: PropTypes.oneOf(["text", "password", "number"]),
//   size: PropTypes.oneOf(["sm", "md", "lg"]),
//   inset: PropTypes.bool,
//   state: PropTypes.oneOf(["rest", "typing", "filled", "disabled", "read-only"]),
//   validation: PropTypes.oneOf(["none", "success", "error"]),
//   direction: PropTypes.oneOf(["rtl", "ltr"]),
//   innerLabel: PropTypes.string,
//   title: PropTypes.string,
//   value: PropTypes.object,
//   headingIcon: PropTypes.element,
//   clearAction: PropTypes.bool,
//   customAction: PropTypes.bool,
//   link: PropTypes.string,
//   dateButtonsClassName: PropTypes.string,
//   linkText: PropTypes.string,
//   caption: PropTypes.string,
//   successMessage: PropTypes.string,
//   errorMessage: PropTypes.string,
//   disabled: PropTypes.bool,
//   className: PropTypes.string,
//   onChange: PropTypes.func,
//   onFocus: PropTypes.func,
//   onBlur: PropTypes.func,
//   field: PropTypes.object.isRequired,
//   form: PropTypes.object.isRequired,
//   inputProps: PropTypes.object,
// };

// import React, { useCallback, useEffect, useState } from "react";
// import PropTypes from "prop-types";
// import {
//   Calendar,
//   CaretLeft,
//   CaretRight,
// } from "@phosphor-icons/react";
// import DatePicker from "react-multi-date-picker";
// import persian from "react-date-object/calendars/persian";
// import persian_fa from "react-date-object/locales/persian_fa";
// import { TagButton } from "./TagButton.jsx";

// export const DateInput = ({
//   id,
//   size = "md",
//   inset = false,
//   state = "rest",
//   validation = "none",
//   direction = "rtl",
//   title,
//   value,
//   link,
//   linkText,
//   successMessage,
//   disabled,
//   className,
//   onChange,
//   field,
//   dateButtonsClassName,
//   inputProps,
//   form: { errors, touched },
// }) => {
//   const [inputClasses, setInputClasses] = useState("");
//   const [fromValue, setFromValue] = useState(new Date(value?.from));
//   const [toValue, setToValue] = useState(new Date(value?.to));
//   const [values, setValues] = useState([fromValue, toValue]);
//   const [selectedOption, setSelectedOption] = useState(value.index || 0);
//   const [showAlert, setShowAlert] = useState(false);

//   const getInputClasses = useCallback(() => {
//     let baseClass = "c-input";
//     let classes = baseClass;
//     classes += ` ${baseClass}-${size}`;
//     if (inset) classes += ` ${baseClass}-inset`;
//     classes += ` ${baseClass}-${state}`;
//     if (touched[field.name] && errors[field.name])
//       classes += ` ${baseClass}-error`;
//     classes += ` ${baseClass}-${direction}`;
//     classes += ` ${className || ""}`;
//     return classes;
//   }, [
//     className,
//     disabled,
//     state,
//     validation,
//     size,
//     inset,
//     direction,
//     errors,
//     touched,
//   ]);

//   useEffect(() => {
//     setInputClasses(getInputClasses());
//   }, [disabled, getInputClasses, validation, errors, touched, successMessage]);

//   const getDateBefore = (daysOrMonths) => {
//     const currentDate = new Date();
//     if (daysOrMonths === 0) {
//       return new Date(
//         currentDate.getFullYear(),
//         currentDate.getMonth(),
//         currentDate.getDate()
//       );
//     } else {
//       const newDate = new Date(currentDate);
//       if (daysOrMonths > 0) {
//         newDate.setDate(newDate.getDate() - daysOrMonths);
//       } else {
//         newDate.setMonth(newDate.getMonth() - Math.abs(daysOrMonths));
//       }
//       return newDate;
//     }
//   };

//   useEffect(() => {
//     if (selectedOption === -1) return;
//     const fromDate = getDateBefore(selectedOption);
//     setFromValue(fromDate);
//     setToValue(new Date());
//     setValues([fromDate, new Date()]);
//     onChange({
//       from: fromDate,
//       to: new Date(),
//       index: selectedOption,
//     });
//     setShowAlert(false); // Reset alert when using predefined options
//   }, [selectedOption]);

//   const handleDateChange = (e) => {
//     if (e.length < 2) return;
//     const fromDate = new Date(e[0]);
//     const toDate = new Date(e[1]);

//     // Calculate the difference in months
//     const diffInMonths =
//       (toDate.getFullYear() - fromDate.getFullYear()) * 12 +
//       (toDate.getMonth() - fromDate.getMonth());

//     if (diffInMonths > 1) {
//       setShowAlert(true);
//       return; // Prevent updating if range exceeds 1 month
//     }

//     setShowAlert(false);
//     setFromValue(fromDate);
//     setToValue(toDate);
//     setValues([fromDate, toDate]);
//     setSelectedOption(-1);
//     onChange({
//       from: fromDate,
//       to: toDate,
//       index: -1,
//     });
//   };

//   const optionIsActive = (index) => {
//     return selectedOption === index;
//   };

//   return (
//     <div className={inputClasses}>
//       <div className={"label-wrapper"}>
//         {title && <label htmlFor={field.name}>{title}</label>}
//         {link && <a href={link}>{linkText}</a>}
//       </div>
//       <div className={"input-wrapper"}>
//         <DatePicker
//           value={values}
//           calendar={persian}
//           locale={persian_fa}
//           calendarPosition="bottom-right"
//           range
//           containerClassName={"datepicker-container"}
//           onChange={handleDateChange}
//           renderButton={(direction, handleClick) => (
//             <button onClick={handleClick}>
//               {direction === "right" ? (
//                 <CaretLeft size={20} />
//               ) : (
//                 <CaretRight size={20} />
//               )}
//             </button>
//           )}
//         />
//         <span className="action-icon text-left">
//           <Calendar />
//         </span>
//       </div>
//       {showAlert && (
//         <div className="text-red-500 text-sm mt-2">
//           محدوده زمانی انتخاب شده نباید بیشتر از یک ماه باشد.
//         </div>
//       )}
//       <div className={"text-right"}>
//         <div
//           className={`flex flex-row justify-between gap-2 my-4 ${dateButtonsClassName}`}
//         >
//           <TagButton
//             isActive={optionIsActive(3)}
//             title={"۳ روز گذشته"}
//             onClick={() => {
//               setSelectedOption(3);
//             }}
//             className={"flex-1"}
//           />
//           <TagButton
//             isActive={optionIsActive(1)}
//             title={"۲۴ ساعت گذشته"}
//             onClick={() => {
//               setSelectedOption(1);
//             }}
//             className={"flex-1"}
//           />
//           <TagButton
//             isActive={optionIsActive(0)}
//             title={"امروز"}
//             onClick={() => {
//               setSelectedOption(0);
//             }}
//             className={"flex-1"}
//           />
//         </div>
//         <div className={"flex flex-row justify-between gap-2 my-4"}>
//           <TagButton
//             isActive={optionIsActive(30)}
//             title={"ماه گذشته"}
//             onClick={() => {
//               setSelectedOption(30);
//             }}
//             className={"flex-1"}
//           />
//           <TagButton
//             isActive={optionIsActive(10)}
//             title={"۱۰ روز گذشته"}
//             onClick={() => {
//               setSelectedOption(10);
//             }}
//             className={"flex-1"}
//           />
//           <TagButton
//             isActive={optionIsActive(7)}
//             title={"هفته گذشته"}
//             onClick={() => {
//               setSelectedOption(7);
//             }}
//             className={"flex-1"}
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// DateInput.propTypes = {
//   id: PropTypes.string,
//   type: PropTypes.oneOf(["text", "password", "number"]),
//   size: PropTypes.oneOf(["sm", "md", "lg"]),
//   inset: PropTypes.bool,
//   state: PropTypes.oneOf(["rest", "typing", "filled", "disabled", "read-only"]),
//   validation: PropTypes.oneOf(["none", "success", "error"]),
//   direction: PropTypes.oneOf(["rtl", "ltr"]),
//   innerLabel: PropTypes.string,
//   title: PropTypes.string,
//   value: PropTypes.object,
//   headingIcon: PropTypes.element,
//   clearAction: PropTypes.bool,
//   customAction: PropTypes.bool,
//   link: PropTypes.string,
//   dateButtonsClassName: PropTypes.string,
//   linkText: PropTypes.string,
//   caption: PropTypes.string,
//   successMessage: PropTypes.string,
//   errorMessage: PropTypes.string,
//   disabled: PropTypes.bool,
//   className: PropTypes.string,
//   onChange: PropTypes.func,
//   onFocus: PropTypes.func,
//   onBlur: PropTypes.func,
//   field: PropTypes.object.isRequired,
//   form: PropTypes.object.isRequired,
//   inputProps: PropTypes.object,
// };

import React, { useCallback, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Calendar, CaretLeft, CaretRight } from "@phosphor-icons/react";
import DatePicker from "react-multi-date-picker";
import TimePicker from "react-multi-date-picker/plugins/time_picker";
import DatePanel from "react-multi-date-picker/plugins/date_panel";
import persian from "react-date-object/calendars/persian";
import persian_fa from "react-date-object/locales/persian_fa";
import { TagButton } from "./TagButton.jsx";

export const DateInput = ({
  id,
  size = "md",
  inset = false,
  state = "rest",
  validation = "none",
  direction = "rtl",
  title,
  value = {},
  link,
  linkText,
  successMessage,
  disabled,
  className,
  onChange,
  field,
  dateButtonsClassName,
  inputProps,
  form: { errors, touched },
}) => {
  const [inputClasses, setInputClasses] = useState("");
  const [fromValue, setFromValue] = useState(
    value?.from ? new Date(value.from) : new Date()
  );
  const [toValue, setToValue] = useState(
    value?.to ? new Date(value.to) : new Date()
  );
  const [values, setValues] = useState([fromValue, toValue]);
  const [selectedOption, setSelectedOption] = useState(value?.index ?? 0);
  const [showAlert, setShowAlert] = useState(false);

  const getInputClasses = useCallback(() => {
    let baseClass = "c-input";
    let classes = `${baseClass} ${baseClass}-${size}`;
    if (inset) classes += ` ${baseClass}-inset`;
    classes += ` ${baseClass}-${state}`;
    if (touched?.[field.name] && errors?.[field.name])
      classes += ` ${baseClass}-error`;
    classes += ` ${baseClass}-${direction}`;
    if (className) classes += ` ${className}`;
    return classes;
  }, [className, state, size, inset, direction, errors, touched, field.name]);

  useEffect(() => {
    setInputClasses(getInputClasses());
  }, [getInputClasses, successMessage]);

  const getDateBefore = (daysOrMonths) => {
    const currentDate = new Date();
    const newDate = new Date(currentDate);

    if (daysOrMonths === 0) {
      return new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        currentDate.getDate()
      );
    }

    if (daysOrMonths > 0) {
      newDate.setDate(currentDate.getDate() - daysOrMonths);
    } else {
      newDate.setMonth(currentDate.getMonth() - Math.abs(daysOrMonths));
    }

    return newDate;
  };

  useEffect(() => {
    if (selectedOption === -1) return;
    const fromDate = getDateBefore(selectedOption);
    const toDate = new Date();
    setFromValue(fromDate);
    setToValue(toDate);
    setValues([fromDate, toDate]);
    onChange?.({
      from: fromDate,
      to: toDate,
      index: selectedOption,
    });
    setShowAlert(false);
  }, [selectedOption]);

  const handleDateChange = (e) => {
    if (!e || e.length < 2) return;

    const fromDate = new Date(e[0]);
    const toDate = new Date(e[1]);

    const diffInMonths =
      (toDate.getFullYear() - fromDate.getFullYear()) * 12 +
      (toDate.getMonth() - fromDate.getMonth());

    if (diffInMonths > 1) {
      setShowAlert(true);
      return;
    }

    setShowAlert(false);
    setFromValue(fromDate);
    setToValue(toDate);
    // setValues([fromDate, toDate]);
    setValues(e);
    setSelectedOption(-1);
    console.log(fromDate);
    onChange?.({
      from: fromDate,
      to: toDate,
      index: -1,
    });
  };

  const getDateConstraints = () => {
    const today = new Date();
    const minDate = new Date(today);
    minDate.setMonth(today.getMonth() - 1);
    const maxDate = new Date(today);
    maxDate.setMonth(today.getMonth() + 1);
    return { minDate, maxDate };
  };

  const { minDate, maxDate } = getDateConstraints();

  const optionIsActive = (index) => selectedOption === index;

  return (
    <div className={inputClasses}>
      <div className="label-wrapper">
        {title && <label htmlFor={field.name}>{title}</label>}
        {link && <a href={link}>{linkText}</a>}
      </div>
      <div className="input-wrapper">
        <DatePicker
          className={"font-body-small"}
          value={values}
          calendar={persian}
          locale={persian_fa}
          calendarPosition="bottom-left"
          dateSeparator="      تا      "
          format="HH:mm - YYYY/MM/DD"
          range
          portal
          showOtherDays
          rangeHover
          containerClassName="datepicker-container"
          onChange={handleDateChange}
          // minDate={minDate}
          // maxDate={maxDate}
          renderButton={(direction, handleClick) => (
            <button onClick={handleClick}>
              {direction === "right" ? (
                <CaretLeft size={20} />
              ) : (
                <CaretRight size={20} />
              )}
            </button>
          )}
          plugins={[
            <TimePicker key="timepicker" position="bottom" hideSeconds />,
            <DatePanel key="datePanel" position="right" />,
          ]}
          style={{
            fontSize: "14px",
            // direction: "ltr",
          }}
        />
        <span className="action-icon text-left">
          <Calendar />
        </span>
      </div>
      {showAlert && (
        <div className="text-red-500 text-sm mt-2">
          محدوده زمانی انتخاب شده نباید بیشتر از یک ماه باشد.
        </div>
      )}
      <div className="text-right">
        <div
          className={`flex flex-row justify-between gap-2 my-4 ${dateButtonsClassName}`}
        >
          <TagButton
            isActive={optionIsActive(3)}
            title="۳ روز گذشته"
            onClick={() => setSelectedOption(3)}
            className="flex-1"
          />
          <TagButton
            isActive={optionIsActive(1)}
            title="۲۴ ساعت گذشته"
            onClick={() => setSelectedOption(1)}
            className="flex-1"
          />
          <TagButton
            isActive={optionIsActive(0)}
            title="امروز"
            onClick={() => setSelectedOption(0)}
            className="flex-1"
          />
        </div>
        <div
          className={`flex flex-row justify-between gap-2 my-4 ${dateButtonsClassName}`}
        >
          <TagButton
            isActive={optionIsActive(30)}
            title="ماه گذشته"
            onClick={() => setSelectedOption(30)}
            className="flex-1"
          />
          <TagButton
            isActive={optionIsActive(10)}
            title="۱۰ روز گذشته"
            onClick={() => setSelectedOption(10)}
            className="flex-1"
          />
          <TagButton
            isActive={optionIsActive(7)}
            title="هفته گذشته"
            onClick={() => setSelectedOption(7)}
            className="flex-1"
          />
        </div>
      </div>
    </div>
  );
};

DateInput.propTypes = {
  id: PropTypes.string,
  type: PropTypes.oneOf(["text", "password", "number"]),
  size: PropTypes.oneOf(["sm", "md", "lg"]),
  inset: PropTypes.bool,
  state: PropTypes.oneOf(["rest", "typing", "filled", "disabled", "read-only"]),
  validation: PropTypes.oneOf(["none", "success", "error"]),
  direction: PropTypes.oneOf(["rtl", "ltr"]),
  innerLabel: PropTypes.string,
  title: PropTypes.string,
  value: PropTypes.object,
  headingIcon: PropTypes.element,
  clearAction: PropTypes.bool,
  customAction: PropTypes.bool,
  link: PropTypes.string,
  dateButtonsClassName: PropTypes.string,
  linkText: PropTypes.string,
  caption: PropTypes.string,
  successMessage: PropTypes.string,
  errorMessage: PropTypes.string,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  onChange: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  field: PropTypes.object.isRequired,
  form: PropTypes.object.isRequired,
  inputProps: PropTypes.object,
};
