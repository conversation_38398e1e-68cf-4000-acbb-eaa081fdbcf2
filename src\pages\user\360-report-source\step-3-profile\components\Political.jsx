import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import { useReport360Store } from "store/report360Store";
import PieChart from "components/Charts/Pie";
import { toPersianNumber } from "utils/helper";
import { useEffect, useState } from "react";
import use360requestStore from "store/360requestStore";
import ExportMenu from "components/ExportMenu/index.jsx";

const PoliticalSpectrum = () => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
  }));

  const profile = sourceReport?.profile;

  // Initialize default tabs
  const tabs = [
    {
      id: "osoolgera",
      title: "حامیان انقلاب اسلامی",
    },
    { id: "barandaz", title: "برانداز" },
    { id: "saltanat", title: "سلطنت" },
    { id: "monafegh", title: "منافق" },
    {
      id: "eslahtalab",
      title: "فعالین مدنی غرب‌گرا",
      count: 0,
    },
    {
      id: "edalatkhah",
      title: "عدالت‌خواه",
    },
    {
      id: "ahmadinezhad",
      title: "احمدی نژادی",
    },
  ];

  const [chartData, setChartData] = useState(
    tabs.map((tab) => ({
      name: tab.title,
      y: tab.count,
    }))
  );
  const [activeTab, setActiveTab] = useState({ title: "هیچکدام", count: 0 });
  const [activeTabColor, setActiveTabColor] = useState("#000000");

  const colors = [
    "#007BFF",
    "#FF6B6B",
    "#4ECDC4",
    "#45B7D1",
    "#96CEB4",
    "#FFEEAD",
    "#D4A5A5",
  ];

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Probability (%)",
      data: chartData.map((item) => item.y * 100),
      time: chartData.map((item) => item.name),
    },
  ];

  const time = chartData.map((item) => item.name);

  useEffect(() => {
    const labelCounts = profile?.political_category?.prob;
    const activeGroup = profile?.political_category?.label;

    if (labelCounts && activeGroup) {
      const updatedChartData = tabs.map((tab) => ({
        name: tab.title,
        y: parseFloat(labelCounts[tab.id]) || 0,
      }));

      setChartData(updatedChartData);

      const foundTab = tabs.find((tab) => tab.id === activeGroup) || {
        title: "هیچکدام",
        count: 0,
      };
      const activeTabIndex = tabs.findIndex((tab) => tab.id === activeGroup);
      setActiveTab({
        title: foundTab.title,
        count: labelCounts[activeGroup] || 0,
      });
      setActiveTabColor(
        activeTabIndex !== -1 ? colors[activeTabIndex] : "#000000"
      );
    }
  }, [date, profile?.political_category]);

  return (
    <Card className="h-full w-full">
      <div className="w-full">
        <div className="flex items-center justify-between px-3">
          <Title title="طیف سیاسی" />
          <ExportMenu
            chartSelector=".political-spectrum-container"
            fileName="political-spectrum"
            series={series}
            time={time}
            excelHeaders={["Category", "Probability (%)"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
            chartTitle="طیف سیاسی"
          />
        </div>
        <div className="w-full political-spectrum-container px-4 flex flex-row gap-4">
          <div className="w-1/3 flex flex-col gap-6 justify-center items-center">
            <span
              className="text-[1.3rem] font-bold"
              style={{ color: activeTabColor }}
            >
              {activeTab.title}
            </span>
            <span className="text-[1.3rem] text-[#7f7f7f] font-bold">
              {toPersianNumber((activeTab.count * 100).toFixed(0))}%
            </span>
          </div>
          <div className="w-2/3">
            <PieChart
              height={160}
              data={chartData}
              showLabelsInTooltip={true}
              colors={colors}
            />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default PoliticalSpectrum;
