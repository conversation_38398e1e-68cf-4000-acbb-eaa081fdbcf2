import { useState, useEffect, useCallback } from "react";
import PropTypes from "prop-types";
import PersonReportsType from "./components/ReportType";
import PersonUserInfo from "./components/UserInfo";
import PersonReportsInfo from "./components/ReportsInfo";
import PersonOverviewTab from "./overviewTab";

import { useReport360Store } from "store/report360Store";
import { Link, useParams } from "react-router-dom";
import use360requestStore from "store/360requestStore";
import NewsBased from "./platform-based-tabs/news";
import TelegramBased from "./platform-based-tabs/telegram";
import InstagramBased from "./platform-based-tabs/instagram";
import TwitterBased from "./platform-based-tabs/twitter";
import CardLoading from "components/ui/CardLoading.jsx";
import {
  fetchStatisticalData,
  fetchProcessData,
  isCachedDataValid,
  hasRequiredCachedData,
} from "../utils";

const Step3Person = ({ isEdit }) => {
  const [activeSpan, setActiveSpan] = useState("overview");
  const [error, setError] = useState();
  const [loading, setLoading] = useState(true);
  const { q } = useParams();

  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    date: { from: state.report?.start_date, to: state.report?.end_date },
    statistical: state.report?.content?.report_info?.statistical,
    process: state.report?.content?.report_info?.process,
  }));
  const updateReportField = use360requestStore(
    (state) => state.updateReportField
  );

  const getStatisticalData = useCallback(async () => {
    setLoading(true);

    try {
      const apiData = await fetchStatisticalData({ date, q, platform: "all" });
      updateReportField("content.report_info.statistical", apiData);
    } catch (error) {
      updateReportField("content.report_info.statistical", {});
      console.error("Statistical Data Error:", error);
    } finally {
      setLoading(false);
    }
  }, [date, q, updateReportField]);

  const getProcessData = useCallback(async () => {
    setLoading(true);

    try {
      const platformData = await fetchProcessData({ date, q, platform: "all" });
      updateReportField("content.report_info.process", platformData);
    } catch (error) {
      updateReportField("content.report_info.process", {});
      console.error("Process Data Error:", error);
    } finally {
      setLoading(false);
    }
  }, [date, q, updateReportField]);

  useEffect(() => {
    if (
      hasRequiredCachedData(sourceReport) &&
      isCachedDataValid(sourceReport.date, date)
    ) {
      setLoading(false);
    } else {
      getStatisticalData();
      getProcessData();
      updateReportField("start_date", date.from);
      updateReportField("end_date", date.to);
    }
  }, [
    date,
    sourceReport,
    getStatisticalData,
    getProcessData,
    updateReportField,
  ]);

  useEffect(() => {
    if (q) {
      updateReportField("content.source_info", { entity: q });
    } else {
      setError("not-found");
    }
  }, [q, updateReportField]);

  if (error === "not-found") {
    return (
      <div className="w-full h-[640px] flex flex-col justify-center items-center">
        <div className="size-12 bg-[url(/empty_folder.png)] bg-no-repeat bg-contain bg-center mb-4"></div>
        <p className="font-body-large mb-2">منبع مورد نظر یافت نشد!</p>
        <p className="font-body-medium">
          بازگشت به
          <Link
            to="/app/report-360/list"
            className="underline underline-offset-4 text-light-primary-text-rest cursor-pointer"
          >
            {" "}
            لیست گزارش‌ها{" "}
          </Link>
        </p>
      </div>
    );
  }
  if (loading)
    return (
      <div className="w-full h-[640px]">
        <CardLoading />
      </div>
    );
  return (
    <>
      <PersonUserInfo isEdit={isEdit} />
      <PersonReportsType activeTab={activeSpan} setActiveSpan={setActiveSpan} />
      <PersonReportsInfo activeSpan={activeSpan} />
      <div className={activeSpan === "overview" ? "block" : "hidden"}>
        <PersonOverviewTab isEdit={isEdit} />
      </div>
      <div className={activeSpan === "twitter" ? "block" : "hidden"}>
        <TwitterBased isEdit={isEdit} />
      </div>
      <div className={activeSpan === "telegram" ? "block" : "hidden"}>
        <TelegramBased isEdit={isEdit} />
      </div>
      <div className={activeSpan === "instagram" ? "block" : "hidden"}>
        <InstagramBased isEdit={isEdit} />
      </div>
      <div className={activeSpan === "news" ? "block" : "hidden"}>
        <NewsBased isEdit={isEdit} />
      </div>
    </>
  );
};

Step3Person.propTypes = {
  isEdit: PropTypes.bool,
};

export default Step3Person;
