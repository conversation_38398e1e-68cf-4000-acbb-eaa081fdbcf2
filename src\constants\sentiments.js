import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>y<PERSON><PERSON> } from "@phosphor-icons/react";

export const SENTIMENTS = [
  {
    label: "مثبت",
    value: "positive",
    icon: <PERSON><PERSON>,
    className: "text-light-success-text-rest",
  },
  {
    label: "خنثی",
    value: "neutral",
    icon: <PERSON><PERSON><PERSON><PERSON>,
    className: "text-light-neutral-text-medium",
  },
  {
    label: "منفی",
    value: "negative",
    icon: SmileySad,
    className: "text-light-error-text-rest",
  },
];

export const SENTIMENTS_ARRAY = SENTIMENTS.map((item) => item.value);

export const SENTIMENTS_LABELS = SENTIMENTS.map((item) => item.label);

export const SENTIMENTS_ICONS = SENTIMENTS.map((item) => item.icon);
