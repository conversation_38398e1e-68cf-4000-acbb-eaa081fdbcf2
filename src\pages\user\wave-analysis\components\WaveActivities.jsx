import BubbleChart from "components/Charts/BubbleChart";
import ExportMenu from "components/ExportMenu/index.jsx";

const WaveActivities = ({ data, setSelectedNode, containerRef }) => {
  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Impact (%)",
      data: (data?.trends || []).map(
        (trend) => (trend.stats.impact || 0) * 100
      ),
      time: (data?.trends || []).map((trend) => trend.title),
    },
    {
      name: "Frequency",
      data: (data?.trends || []).map((trend) => trend.stats.posts || 0),
      time: (data?.trends || []).map((trend) => trend.title),
    },
  ];

  const time = (data?.trends || []).map((trend) => trend.title);

  return (
    <div
      ref={containerRef}
      className="bg-light-neutral-surface-card rounded-lg py-2 shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]"
    >
      <div className="flex justify-end p-4">
        <ExportMenu
          chartSelector=".wave-activities-container"
          fileName="wave-activities"
          series={series}
          time={time}
          excelHeaders={["Trend", "Impact (%)", "Frequency"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
        />
      </div>
      <BubbleChart data={data} setSelectedNode={setSelectedNode} />
    </div>
  );
};

export default WaveActivities;
