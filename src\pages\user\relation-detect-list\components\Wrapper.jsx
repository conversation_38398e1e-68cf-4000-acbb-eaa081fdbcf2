import { Plus } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { Link } from "react-router-dom";

const Wrapper = ({ children }) => {
  return (
    <div className="p-4 flex flex-col gap-4 rounded-lg bg-light-neutral-surface-card shadow-[0px_2px_20px_0px_#0000000D]">
      <div className="flex items-center justify-between">
        <div className="font-subtitle-medium text-light-neutral-text-high mb-4">
          لیست روابط
        </div>
        <Link to={"/app/relation-detect/create"} className="">
          <CButton
            rightIcon={<Plus />}
            width={200}
            className={"[direction:ltr]"}
            mode="outline"
          >
            رابطه جدید
          </CButton>
        </Link>
      </div>
      <div>
        <ul className="font-overline-medium text-light-neutral-text-medium grid grid-cols-5">
          <li>عنوان</li>
          <li>تاریخ ایجاد</li>
          <li>تاریخ به‌روزرسانی</li>
        </ul>
      </div>
      {children}
    </div>
  );
};

export default Wrapper;
