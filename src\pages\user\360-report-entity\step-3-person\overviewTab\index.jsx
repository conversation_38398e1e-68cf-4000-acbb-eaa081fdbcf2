// import AboutPerson from "../components/AboutPerson";
// import PersonFrequentEntities from "../components/FrequentEntities";
// import PersonPoliticalSpectrum from "../components/Political";
// import PersonThoughtLine from "../components/ThoughtLine";

import AllPlatformContent from "../components/AllPlatformContent";
import OverviewProcess from "../components/overviewProcess.jsx";
import PropTypes from "prop-types";

const PersonOverviewTab = ({ isEdit }) => {
  return (
    <div className="flex flex-col gap-3 items-center mx-4 pl-2">
      {/* <div className="flex gap-3 w-[99%]">
        <div className="flex flex-1">
          <PersonThoughtLine />
        </div>
        <div className="flex flex-1">
          <PersonPoliticalSpectrum />
        </div>
      </div>
      <div className="flex gap-3 w-[99%]">
        <div className="flex flex-1">
          <PersonFrequentEntities />
        </div>
        <div className="flex flex-1">
          <AboutPerson />
        </div>
      </div> */}
      <AllPlatformContent isUpdate={isEdit} />
      <OverviewProcess activePlatform="all" isUpdate={isEdit} />
    </div>
  );
};

PersonOverviewTab.propTypes = {
  isEdit: PropTypes.bool.isRequired,
};

export default PersonOverviewTab;
