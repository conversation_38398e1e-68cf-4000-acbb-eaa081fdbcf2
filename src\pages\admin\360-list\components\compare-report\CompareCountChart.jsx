import { fixPercentToShow } from "utils/helper";
import Doughnut from "components/Charts/Doughnut.jsx";
import { useEffect, useState } from "react";
import { useMeasure } from "react-use";
import Divider from "components/ui/Divider.jsx";
import { SpinnerGap } from "@phosphor-icons/react";
import MediaBadge from "components/ui/MediaBadge.jsx";
import { Card } from "components/ui/Card.jsx";
import HorizontalBar from "components/Charts/HorizontalBar.jsx";

const CompareCountChart = ({ filter }) => {
  return (
    <Card className="flex flex-col gap-2 h-full">
      <div className="flex items-center gap-1">
        <p className="font-subtitle-large">
          کاربرانی که بیشترین مقایسه‌ها را ثبت کرده اند
        </p>
      </div>

      <HorizontalBar
        colors={["#ECA213", "#6F5CD1", "#D1A8FF"]}
        info={[{ count: 550 }, { count: 440 }, { count: 330 }]}
        loading={false}
        categories={["چهارتایی", "سه‌تایی", "دوتایی"]}
        minWidth={"15rem"}
        maxWidth="100%"
        pointWidth={50}
        height={250}
      />

      {/*<ColumnChart*/}
      {/*  xAxisCategory={[*/}
      {/*    "فرهنگ",*/}
      {/*    "اجتماعی",*/}
      {/*    "ورزشی",*/}
      {/*    "سیاسی",*/}
      {/*    "اقتصادی",*/}
      {/*    "علمی",*/}
      {/*    "هنر و رسانه",*/}
      {/*    "روزمره",*/}
      {/*    "بین الملل",*/}
      {/*  ]}*/}
      {/*  seriesColor={"#1DCEA3"}*/}
      {/*/>*/}
    </Card>
  );
};

export default CompareCountChart;
