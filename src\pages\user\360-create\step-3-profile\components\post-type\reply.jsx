import { ChatCircle } from "@phosphor-icons/react";
import PropTypes from "prop-types";

const Reply = ({ selectMedia, staticData }) => {
  return (
    <div className="border border-low bg-[#f4f4f4] rounded-lg relative">
      <div className="p-4 flex flex-col justify-between gap-4 min-h-40 h-full w-full [direction:rtl] relative">
        <div className="flex justify-between items-center">
          <div className="flex gap-4 items-center">
            <div className="flex gap-2 relative">
              <div
                className="size-10 rounded-full bg-contain ml-2 relative z-10"
                style={{
                  backgroundImage: `url(${staticData?.centralNodeAvatar})`,
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "contain",
                  backgroundPosition: "center center",
                }}
              ></div>
              <div className="flex flex-col flex-1">
                <span className="font-subtitle-medium text-light-neutral-text-high">
                  {staticData.centralNodeTitle}
                </span>
                <span className="font-overline-medium text-light-neutral-text-medium">
                  {`${staticData.centralNodeUsername}`}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          dir="ltr"
          className="font-body-medium text-right"
          style={{ paddingRight: "3rem" }}
        >
          {staticData.nodeText}
        </div>
        <div
          className="flex items-center justify-between"
          style={{ paddingRight: "3rem" }}
        >
          <span
            dir="ltr"
            className="font-overline-medium text-light-neutral-text-medium"
          >
            {new Date(staticData.time).toLocaleString("fa-IR")}
          </span>
          <div className="flex gap-4">
            {selectMedia.twitter.sub.map(({ icon, value }, index) => (
              <div className="flex items-center gap-1" key={index}>
                <span className="font-overline-medium text-light-neutral-text-medium">
                  {value}
                </span>
                <div>{icon}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Connecting Line */}
      <div className="absolute right-[35px] top-[60px] bottom-[150px] w-[2px] bg-[#d1d6dd]"></div>

      <div className="p-4 flex flex-col rounded-lg justify-between gap-4 min-h-40 h-full w-full [direction:rtl] relative">
        <div className="flex justify-between">
          <div className="flex gap-4 items-center">
            <div className="flex gap-2 relative">
              <div
                className="size-10 rounded-full bg-contain ml-2 relative z-10"
                style={{
                  backgroundImage: `url(${staticData.avatar})`,
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "contain",
                  backgroundPosition: "center center",
                }}
              ></div>
              <div className="flex flex-col flex-1">
                <span className="font-subtitle-medium text-light-neutral-text-high">
                  {staticData.title}
                </span>
                <span className="font-overline-medium text-light-neutral-text-medium">
                  {`${staticData.username}`}
                </span>
              </div>
            </div>
          </div>
          <span className="font-body-small font-bold flex gap-2 bg-[#ededf3] w-[72px] h-[26px] justify-center rounded-md items-center">
            <ChatCircle />
            ریپلای
          </span>
        </div>
        <div
          dir="ltr"
          className="font-body-medium text-right"
          style={{ paddingRight: "3rem" }}
        >
          {staticData.content}
        </div>
        <div
          className="flex items-center justify-between"
          style={{ paddingRight: "3rem" }}
        >
          <span
            dir="ltr"
            className="font-overline-medium text-light-neutral-text-medium"
          >
            {new Date(staticData.nodeTime).toLocaleString("fa-IR")}
          </span>
          <div className="flex gap-4">
            {selectMedia.twitter.sub.map(({ icon, value }, index) => (
              <div className="flex items-center gap-1" key={index}>
                <span className="font-overline-medium text-light-neutral-text-medium">
                  {value}
                </span>
                <div>{icon}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
Reply.propTypes = {
  type: PropTypes.string,
  selectMedia: PropTypes.shape({
    twitter: PropTypes.shape({
      avatar: PropTypes.string.isRequired,
      sub: PropTypes.arrayOf(
        PropTypes.shape({
          icon: PropTypes.element.isRequired,
          value: PropTypes.string.isRequired,
        })
      ).isRequired,
    }).isRequired,
  }).isRequired,
  staticData: PropTypes.shape({
    title: PropTypes.string.isRequired,
    username: PropTypes.string.isRequired,
    time: PropTypes.string.isRequired,
    content: PropTypes.string.isRequired,
    avatar: PropTypes.string.isRequired,
    centralNodeUsername: PropTypes.string.isRequired,
    centralNodeTitle: PropTypes.string.isRequired,
    centralNodeAvatar: PropTypes.string.isRequired,
  }).isRequired,
};

export default Reply;
