import { useState, createContext, useContext, useEffect } from "react";
import PropTypes from "prop-types";

const LayoutStateContext = createContext();

export const useLayoutContext = () => useContext(LayoutStateContext);

export function LayoutProvider({ children }) {
  const isSmall = window.innerWidth < 640;
  const [isSidebarOpened, setIsSidebarOpened] = useState(!isSmall);
  const [breadcrumb, setBreadcrumb] = useState([]);
  const [openDrawer, setOpenDrawer] = useState(false);

  const isDarkModeActive = () => {
    const storedMode = localStorage.getItem("isDarkMode");
    return storedMode ? JSON.parse(storedMode) : false;
  };

  const [isDarkMode, setIsDarkMode] = useState(isDarkModeActive());

  useEffect(() => {
    localStorage.setItem("isDarkMode", JSON.stringify(isDarkMode));
  }, [isDarkMode]);

  return (
    <div dir={"rtl"} className={isDarkMode ? "dark" : "light"}>
      <LayoutStateContext.Provider
        value={{
          breadcrumb,
          setBreadcrumb,
          isSidebarOpened,
          setIsSidebarOpened,
          setIsDarkMode,
          openDrawer,
          setOpenDrawer,
        }}
      >
        {children}
      </LayoutStateContext.Provider>
    </div>
  );
}

LayoutProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
