import { useRef, useEffect, useState, memo } from "react";
import ForceGraph2D from "react-force-graph-2d";
import * as d3 from "d3";
import { MapPin, Repeat, PencilSimpleLine, At } from "@phosphor-icons/react";
import defaultNodeImage from "assets/images/default.png";
import { useLocation } from "react-router-dom";
import NodeDrawer from "pages/user/360-create/step-3-profile/components/NodeDrawer";
import { notification } from "utils/helper";
import { useRelationStore } from "store/relationDetectStore";
import advanceSearch from "service/api/advanceSearch";

const GraphContent = ({ finalGraphData }) => {
  const edgeDetails = useRelationStore((state) => state.relation.edgeDetails);
  const [postCounts, setPostCounts] = useState({
    repost: 0,
    quote: 0,
    mention: 0,
  });
  const [quote, setQuote] = useState([]);
  const [nodeInfo, setNodeInfo] = useState(null);
  const [totalCounts, setTotalCounts] = useState(0);
  const [showMore, setShowMore] = useState(false);
  const { state } = useLocation();
  const fgRef = useRef();
  const [dimensions, setDimensions] = useState({ width: 800, height: 670 });
  const containerRef = useRef();
  const imageCache = useRef({});
  const [graphData, setGraphData] = useState({
    nodes: [],
    links: [],
    node_links_count: {},
  });
  // Preload defaultNodeImage on component mount
  useEffect(() => {
    const defaultImg = new Image();
    imageCache.current["default"] = {
      img: defaultImg,
      status: "loading",
    };
    defaultImg.onload = () => {
      imageCache.current["default"] = {
        img: defaultImg,
        status: "loaded",
      };
    };
    defaultImg.onerror = () => {
      console.warn(`Failed to load default image: ${defaultNodeImage}`);
      imageCache.current["default"] = {
        img: null,
        status: "failed",
      };
    };
    defaultImg.src = defaultNodeImage;
  }, []);
  // Initialize graph data with useLocation state
  useEffect(() => {
    const initialNodes = state?.data?.nodes || [];
    const initialLinks = [
      ...(state?.data?.links?.mention?.map((link) => ({
        source: link?.source,
        target: link?.target,
        type: "mention",
      })) || []),
      ...(state?.data?.links?.reply?.map((link) => ({
        source: link?.source,
        target: link?.target,
        type: "reply",
      })) || []),
      ...(state?.data?.links?.quote?.map((link) => ({
        source: link?.source,
        target: link?.target,
        type: "quote",
      })) || []),
      ...(state?.data?.links?.retweet?.map((link) => ({
        source: link?.source,
        target: link?.target,
        type: "retweet",
      })) || []),
    ];
    const initialNodeLinksCount = state?.data?.node_links_count || {};
    setGraphData({
      nodes: initialNodes,
      links: initialLinks,
      node_links_count: initialNodeLinksCount,
    });
  }, [state]);
  // Merge finalGraphData with existing graph data
  useEffect(() => {
    if (
      finalGraphData &&
      (finalGraphData.nodes?.length || finalGraphData.links)
    ) {
      setGraphData((prev) => {
        const existingNodeNames = new Set(
          prev.nodes.map((node) => node.node_name)
        );
        const newNodes =
          finalGraphData.nodes?.filter(
            (node) => !existingNodeNames.has(node.node_name)
          ) || [];
        const updatedNodes = [...prev.nodes, ...newNodes];

        const existingLinks = new Set(
          prev.links.map((link) => `${link.source}-${link.target}-${link.type}`)
        );
        const newLinks = [
          ...(finalGraphData.links?.mention?.map((link) => ({
            source: link?.source,
            target: link?.target,
            type: "mention",
          })) || []),
          ...(finalGraphData.links?.reply?.map((link) => ({
            source: link?.source,
            target: link?.target,
            type: "reply",
          })) || []),
          ...(finalGraphData.links?.quote?.map((link) => ({
            source: link?.source,
            target: link?.target,
            type: "quote",
          })) || []),
          ...(finalGraphData.links?.retweet?.map((link) => ({
            source: link?.source,
            target: link?.target,
            type: "retweet",
          })) || []),
        ].filter(
          (link) =>
            !existingLinks.has(`${link.source}-${link.target}-${link.type}`)
        );
        const updatedNodeLinksCount = {
          ...prev.node_links_count,
          ...(finalGraphData.node_links_count || {}),
        };
        return {
          nodes: updatedNodes,
          links: [...prev.links, ...newLinks],
          node_links_count: updatedNodeLinksCount,
        };
      });
    }
  }, [finalGraphData]);
  // Preload images for all nodes with error handling
  useEffect(() => {
    graphData.nodes.forEach((node) => {
      if (
        node.node_type !== "source" &&
        node.node_type !== "location" &&
        node.node_type !== "organization" &&
        node.node_type !== "person" &&
        node?.node_name // Ensure node_name exists
      ) {
        const cached = imageCache.current[node.node_name];
        if (cached && cached.status !== "loading") return; // Skip if already loaded or failed

        const imgSrc =
          node?.node_avatar &&
          node.node_avatar !==
            "https://s3.synappse.ir/twitter/profiles/None.jpg"
            ? node.node_avatar
            : defaultNodeImage;
        // Skip invalid URLs
        if (!imgSrc || imgSrc === "null" || imgSrc === "undefined") {
          imageCache.current[node.node_name] = {
            img: null,
            status: "failed",
          };
          return;
        }
        const img = new Image();
        imageCache.current[node.node_name] = {
          img,
          status: "loading",
        };
        img.onload = () => {
          imageCache.current[node.node_name] = {
            img,
            status: "loaded",
          };
        };
        img.onerror = () => {
          console.warn(
            `Failed to load image for node ${node.node_name}: ${imgSrc}`
          );
          imageCache.current[node.node_name] = {
            img: null,
            status: "failed",
          };
        };
        img.src = imgSrc;
      }
    });
  }, [graphData.nodes]);
  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  // Configure D3 forces
  useEffect(() => {
    const graph = fgRef?.current;
    if (graph) {
      graph.d3Force("charge", d3.forceManyBody().strength(-8));
      graph.d3Force("link").distance(50);
      graph.d3Force("center").strength(0.1);
    }
  }, []);

  // Handle link click and send request
  const handleLinkClick = async (link) => {
    const payload = {
      start_date: edgeDetails?.timeSettings?.from,
      end_date: edgeDetails?.timeSettings?.to,
      order: "asc",
      page: 1,
      platform: "twitter",
      q: link?.target?.node_name,
      report_type: "related_content",
      rows: 20,
      sort: "date",
      sources: [link?.source?.node_name],
    };

    try {
      const response = await advanceSearch?.search(payload);
      const responseDataArray = response?.data?.data?.twitter || [];
      const sourceNode = graphData.nodes.find(
        (node) => node.node_name === link.source.node_name
      );
      const targetNode = graphData.nodes.find(
        (node) => node.node_name === link.target.node_name
      );
      const combinedDataArray = responseDataArray.map((responseData) => ({
        avatar:
          responseData?.avatar || targetNode?.node_avatar || defaultNodeImage,
        originalAvatar:
          responseData?.original_avatar || targetNode?.original_avatar || null,
        followerCount:
          responseData?.follower_count || targetNode?.follower_count || 0,
        userName: responseData?.user_name || targetNode?.node_name || null,
        userTitle: responseData?.user_title || targetNode?.node_title || null,
        quote: responseData?.quote || null,
        postType: responseData?.post_type || null,
        text: responseData?.text || null,
        centralNodeAvatar: targetNode?.node_avatar || defaultNodeImage,
        centralNodeTitle: targetNode?.node_title || null,
        centralNodeUsername: targetNode?.node_name || null,
        centralNodeFollower: targetNode?.follower_count || 0,
        quoteName: responseData?.quote?.user_name || null,
        quoteTitle: responseData?.quote?.user_title || null,
        quoteAvatar: responseData?.quote?.avatar || null,
        nodeCount: responseDataArray.length,
        totalCount: responseDataArray.length,
      }));
      setNodeInfo(combinedDataArray);
      setShowMore(true);
      const counts = {
        repost: responseDataArray.filter(
          (item) => item.post_type === "retweet" || item.post_type === "repost"
        ).length,
        quote: responseDataArray.filter(
          (item) => item.post_type === "quote" || item.post_type === "qoute"
        ).length,
        mention: responseDataArray.filter(
          (item) => item.post_type === "mention" || item.post_type === "post"
        ).length,
      };
      setPostCounts(counts);
      setTotalCounts(responseDataArray.length);
      setQuote(
        responseDataArray.map((responseData) => ({
          ...responseData,
          avatar: responseData?.avatar || defaultNodeImage,
          centralNodeAvatar: sourceNode?.node_avatar || defaultNodeImage,
          clickedNodeTitle:
            targetNode?.node_title || responseData?.user_title || null,
          clickedNodeUsername:
            targetNode?.node_name || responseData?.user_name || null,
          quoteName: responseData?.quote?.user_name || null,
          quoteTitle: responseData?.quote?.user_title || null,
          quoteAvatar: responseData?.quote?.avatar || null,
          postUrl: responseData?.post_url || null,
          platform: "twitter",
        }))
      );
    } catch (error) {
      notification.error(
        error?.response?.data?.message || "خطا در دریافت محتوای مرتبط",
        <MapPin size={20} className="text-light-error-background-rest" />
      );
    }
  };
  const renderNode = (node, ctx) => {
    const imgSize = 15;
    const borderWidth = 0.5;
    const borderColor = "#9198AD";
    const padding = 0.5;

    if (node.node_type === "location") {
      ctx.save();
      ctx.beginPath();
      ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
      ctx.closePath();
      ctx.clip();
      ctx.fillStyle = "#FF5733";
      ctx.fill();
      ctx.font = `${imgSize}px phosphor`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillStyle = "white";
      ctx.fillText("📍", node.x, node.y);
      ctx.restore();
    } else if (node.node_type === "organization") {
      ctx.save();
      ctx.beginPath();
      ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
      ctx.closePath();
      ctx.clip();
      ctx.fillStyle = "#4A90E2";
      ctx.fill();
      ctx.font = `${imgSize}px phosphor`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillStyle = "white";
      ctx.fillText("🏢", node.x, node.y);
      ctx.restore();
    } else if (node.node_type === "person") {
      ctx.save();
      ctx.beginPath();
      ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
      ctx.closePath();
      ctx.clip();
      ctx.fillStyle = "#2ECC71";
      ctx.fill();
      ctx.font = `${imgSize}px phosphor`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillStyle = "white";
      ctx.fillText("👤", node.x, node.y);
      ctx.restore();
    } else {
      const cached = imageCache.current[node?.node_name];
      if (cached?.status === "loaded" && cached.img) {
        ctx.save();
        ctx.beginPath();
        ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
        ctx.closePath();
        ctx.clip();
        ctx.drawImage(
          cached.img,
          node.x - imgSize / 2,
          node.y - imgSize / 2,
          imgSize,
          imgSize
        );
        ctx.restore();
      } else {
        // Use defaultNodeImage from cache
        const defaultCached = imageCache.current["default"];
        if (defaultCached?.status === "loaded" && defaultCached.img) {
          ctx.save();
          ctx.beginPath();
          ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
          ctx.closePath();
          ctx.clip();
          ctx.drawImage(
            defaultCached.img,
            node.x - imgSize / 2,
            node.y - imgSize / 2,
            imgSize,
            imgSize
          );
          ctx.restore();
        } else {
          // Fallback to gray circle if default image is unavailable
          ctx.save();
          ctx.beginPath();
          ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
          ctx.closePath();
          ctx.clip();
          ctx.fillStyle = "#CCCCCC"; // Placeholder color
          ctx.fill();
          ctx.restore();
        }
      }
    }
    // Draw border
    ctx.beginPath();
    ctx.arc(
      node.x,
      node.y,
      imgSize / 2 + padding + borderWidth / 2,
      0,
      Math.PI * 2
    );
    ctx.lineWidth = borderWidth;
    ctx.strokeStyle = borderColor;
    ctx.stroke();
    ctx.closePath();
  };

  const renderLink = (link, ctx, globalScale) => {
    // Color mapping for link types
    const colorMap = {
      reply: "#1DCEA3",
      quote: "#DB6DE5",
      retweet: "#FCB353",
      mention: "#6D72E5",
    };

    const sourceCount = graphData.node_links_count[link.source.node_name] || 0;
    const targetCount = graphData.node_links_count[link.target.node_name] || 0;
    const linkColor =
      sourceCount > 1 || targetCount > 1
        ? "#000000"
        : colorMap[link.type] || "#9198AD";

    // Node size from renderNode (imgSize / 2 is the radius)
    const nodeRadius = 7.5 / globalScale; // imgSize = 15 in renderNode, so radius = 15 / 2
    const padding = 1 / globalScale; // Padding from renderNode
    const borderWidth = 1 / globalScale; // Border width from renderNode

    // Calculate the total node offset (radius + padding + border)
    const nodeOffset = nodeRadius + padding + borderWidth / 2;

    // Calculate the vector from source to target
    const dx = link.target.x - link.source.x;
    const dy = link.target.y - link.source.y;
    const length = Math.sqrt(dx * dx + dy * dy);

    // Avoid division by zero for self-loops
    if (length === 0) return;

    // Normalize the direction vector
    const unitDx = dx / length;
    const unitDy = dy / length;

    // Calculate start and end points, offset from node boundaries
    const startX = link.source.x + unitDx * nodeOffset;
    const startY = link.source.y + unitDy * nodeOffset;
    const endX = link.target.x - unitDx * nodeOffset;
    const endY = link.target.y - unitDy * nodeOffset;

    // Draw the link line between offset points
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = linkColor;
    ctx.lineWidth = 0.8 / globalScale;
    ctx.stroke();

    // Draw custom arrowhead near the target
    if (link.source !== link.target) {
      // Avoid drawing arrows for self-loops
      const arrowLength = 5; // Fixed size, no globalScale division
      const arrowWidth = 5; // Fixed size, no globalScale division

      // Calculate the arrow position near the target
      const t = 0.9; // Position near the target (0.9 is close to endX/endY)
      const x = startX + (endX - startX) * t;
      const y = startY + (endY - startY) * t;
      // Calculate the angle of the link
      const angle = Math.atan2(endY - startY, endX - startX);
      // Draw the arrowhead
      ctx.save();
      ctx.beginPath();
      ctx.translate(x, y);
      ctx.rotate(angle);
      ctx.moveTo(0, 0);
      ctx.lineTo(-arrowLength, -arrowWidth / 2);
      ctx.lineTo(-arrowLength, arrowWidth / 2);
      ctx.closePath();
      ctx.fillStyle = linkColor; // Use the same color as the link
      ctx.fill();
      ctx.restore();
    }
  };
  const post_type_badges = [
    {
      name: "ری‌پست",
      engName: "repost",
      icon: <Repeat color="#FF9408" />,
      count: postCounts?.repost,
      bgColor: "#f9dbb2",
    },
    {
      name: "کوت",
      engName: "quote",
      icon: <PencilSimpleLine color="#DB6DE5" />,
      count: postCounts?.quote,
      bgColor: "#efd0f5",
    },
    {
      name: "منشن",
      engName: "mention",
      icon: <At color="#1DCEA3" />,
      count: postCounts?.mention,
      bgColor: "#b6ede1",
    },
  ];

  return (
    <>
      <div className="h-[670px]" ref={containerRef}>
        <ForceGraph2D
          ref={fgRef}
          graphData={graphData}
          dagMode={null}
          dagLevelDistance={300}
          backgroundColor="transparent"
          nodeRelSize={1}
          nodeId="node_name"
          nodeVal={(node) =>
            graphData?.node_links_count?.[node?.node_name] || 1
          }
          nodeLabel="node_title"
          linkDirectionalParticles={2}
          linkDirectionalParticleWidth={0}
          // linkDirectionalArrowLength={10}
          linkDirectionalArrowRelPos={0.5}
          d3VelocityDecay={0.3}
          width={dimensions?.width}
          height={dimensions?.height}
          zoom={0.8}
          maxZoom={10}
          minZoom={0.4}
          nodeCanvasObject={renderNode}
          nodeCanvasObjectMode={() => "replace"}
          linkCanvasObject={renderLink}
          onLinkClick={handleLinkClick}
        />
      </div>
      {showMore && nodeInfo && nodeInfo.length > 0 && (
        <NodeDrawer
          quote={quote}
          badges={post_type_badges}
          nodeInfo={nodeInfo}
          totalCounts={totalCounts}
          setShowMore={setShowMore}
          influence={true}
        />
      )}
    </>
  );
};

export default memo(GraphContent);
