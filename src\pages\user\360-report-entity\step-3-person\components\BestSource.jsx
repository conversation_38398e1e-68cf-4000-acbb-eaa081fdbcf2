import { useState, useEffect } from "react";
import Divider from "components/ui/Divider";
import RadialBar from "components/Charts/RadialBar";
import DropDown from "components/ui/DropDown";
import PropTypes from "prop-types";
import { Card } from "components/ui/Card";
import { useReport360Store } from "store/report360Store";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import { parseNumber } from "utils/helper";
import use360requestStore from "store/360requestStore";
import usePlatformDataStore from "store/person360platform";
import { RESOURCE_SORT_TYPE } from "constants/sort-type.js";
import ExportMenu from "components/ExportMenu/index.jsx";
import { useNavigate } from "react-router-dom";
import Popup from "components/ui/PopUp";
import { UserSwitch } from "@phosphor-icons/react";

const PersonBestSource = ({ activePlatform, isUpdate }) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sort, setSort] = useState({ fa: "محتوا", en: "date" });
  const [selectedItem, setSelectedItem] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const { date, entity, type } = useReport360Store((state) => state.report);
  const setReport = useReport360Store((state) => state.setReport);
  const locationEntityCharts = use360requestStore(
    (state) => state.locationEntityCharts
  );
  const report = use360requestStore((state) => state.report);
  const { platformData, setPlatformData } = usePlatformDataStore();
  const navigate = useNavigate();
  const reportType =
    type === "topic" ? "entity" : type === "location" ? "location" : null;
  const chartData = report?.[reportType]?.[activePlatform]?.top_source;

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Source Count",
      data: data.map((item) => item.count),
      time: data.map((item) => item.title || item.key),
    },
  ];

  const time = data.map((item) => item.title || item.key);

  const getData = async (abortController) => {
    // Check cache first
    const cachedData = platformData[activePlatform]?.topSource;
    if (cachedData && !isUpdate) {
      setData(cachedData.slice(0, 5));
      setLoading(false);
      return;
    }

    // Use chartData if isUpdate is true and data exists
    if (isUpdate && chartData?.length > 0) {
      setData(chartData.slice(0, 5));
      setPlatformData(activePlatform, "topSource", chartData);
      setLoading(false);
      return;
    }

    // Fetch new data
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          platform: activePlatform,
          q: entity,
          date,
          sort: sort.en,
        },
        "top_sources"
      );
      const res = await advanceSearch.search(requestData, abortController);

      const platformDataResponse = res?.data?.data?.[activePlatform] || [];

      const transformedData = platformDataResponse.map(
        ({ id, key, count, title, avatar }) => ({
          id,
          key: key,
          count: count,
          title: title,
          avatar: avatar,
        })
      );

      locationEntityCharts(
        transformedData,
        `top_source`,
        activePlatform,
        reportType
      );

      setData(transformedData.slice(0, 5));
      setPlatformData(activePlatform, "topSource", transformedData);
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error("Error fetching data:", error);
      }
      setData([]);
    } finally {
      if (!abortController.signal.aborted) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    getData(abortController);
    return () => abortController.abort();
  }, [date, entity, sort, activePlatform, isUpdate]);

  useEffect(() => {
    setSort({ fa: "محتوا", en: "date" });
  }, [activePlatform]);

  const openConfirmPopup = (item) => {
    setSelectedItem(item);
    setIsPopupOpen(true);
  };

  const closeConfirmPopup = () => {
    setIsPopupOpen(false);
    setSelectedItem(null);
  };

  const submitHandler = () => {
    if (selectedItem) {
      handleProfileClick(selectedItem);
      setIsPopupOpen(false);
    }
  };

  const handleProfileClick = (profile) => {
    try {
      const selectedProfile = {
        id: profile?.id,
        user_title: profile?.title,
        user_name: profile?.key,
        platform: activePlatform,
        avatar: profile?.avatar,
      };

      setReport({
        isFromTopSource: true,
        step: 3,
        type: "profile",
        profile: selectedProfile,
      });

      navigate("/app/report-360/create");
    } catch (e) {
      console.error("Profile click error:", e);
    }
  };

  return (
    <Card className="person-best-source-container flex flex-col h-full">
      <div className="flex items-center justify-between px-3">
        <div className="flex gap-5 items-center">
          <div className="font-subtitle-large">برترین منابع</div>
          <ExportMenu
            chartSelector=".person-best-source-container"
            fileName="person-best-source"
            series={series}
            time={time}
            excelHeaders={["Source", "Count"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
          />
        </div>
        <div className="flex flex-row items-center gap-4 [direction:rtl] relative z-30">
          <DropDown
            title="نمایش بر اساس"
            subsets={RESOURCE_SORT_TYPE[activePlatform]?.map((item) => item.fa)}
            selected={sort.fa}
            setSelected={(value) => {
              setSort(
                RESOURCE_SORT_TYPE[activePlatform]?.filter(
                  (item) => item.fa === value
                )[0]
              );
            }}
          />
        </div>
      </div>
      <div className="py-4">
        <Divider />
      </div>

      <div className="relative">
        <div className={`w-full grid grid-cols-12 gap-6 h-96`}>
          {loading ? (
            <div className="h-[270px] flex items-center justify-center font-subtitle-medium col-span-12">
              در حال بارگذاری...
            </div>
          ) : data?.length === 0 ? (
            <div className="h-[270px] flex items-center justify-center font-subtitle-medium col-span-12">
              داده ای برای نمایش وجود ندارد
            </div>
          ) : (
            <>
              <div
                className={`col-span-${
                  activePlatform === "news" ? "6" : "7"
                } w-full flex flex-col gap-4 [direction:rtl]`}
              >
                <div
                  className={`grid ${
                    activePlatform === "news"
                      ? "grid-cols-[5fr_1fr_1fr_1fr]"
                      : "grid-cols-[5fr_1fr_1fr_1fr]"
                  } *:font-body-medium *:text-light-neutral-text-medium`}
                >
                  <div>منبع</div>
                  <div>{sort.fa}</div>
                </div>

                {data?.slice(0, 5).map((item) => (
                  <div
                    className={`grid ${
                      activePlatform === "news"
                        ? "grid-cols-[5fr_1fr_1fr_1fr]"
                        : "grid-cols-[5fr_1fr_1fr_1fr]"
                    } items-center p-1 cursor-pointer rounded-lg hover:bg-light-primary-background-highlight`}
                    key={item.id}
                    onClick={() => openConfirmPopup(item)}
                  >
                    <div className="flex gap-2 items-center">
                      <div
                        className="size-10 rounded-full"
                        style={{
                          backgroundImage: item?.avatar
                            ? `url(${item.avatar})`
                            : "url(/logo_small.png)",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center center",
                          backgroundSize: "contain",
                        }}
                      ></div>
                      <div>
                        <div className="font-subtitle-medium">
                          {item?.title?.slice(0, 20) ||
                            item?.title ||
                            item?.key}
                        </div>
                        <div className="font-overline-medium">
                          {item.key &&
                            `${item.key} ${
                              activePlatform !== "news" ? "@" : ""
                            }`}
                        </div>
                      </div>
                    </div>
                    <div className="font-body-bold-medium">
                      {parseNumber(item?.count)}
                    </div>
                  </div>
                ))}
              </div>
              <div className="col-span-5">
                {!!data?.filter((item) => item.count).length && (
                  <RadialBar
                    data={data?.slice(0, 5).map((item) => ({
                      key: item.key,
                      count: item.count,
                      title: item.key || null,
                      y: item.count,
                    }))}
                  />
                )}
              </div>
            </>
          )}
        </div>
      </div>
      <Popup
        isOpen={isPopupOpen}
        onClose={closeConfirmPopup}
        submitHandler={submitHandler}
        title="آیا می‌خواهید گزارش های 360 این منبع نمایش داده شود؟"
        agreeButton="بله"
        cancleButton="خیر"
        icon={<UserSwitch size={45} />}
      >
        <p className="py-5 font-body-medium">
          توجه کنید که با کلیک برروی گزینه بله به صفحه گزارشات 360 این منبع
          منتقل خواهید شد.
        </p>
      </Popup>
    </Card>
  );
};

PersonBestSource.propTypes = {
  activePlatform: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool,
};

export default PersonBestSource;
