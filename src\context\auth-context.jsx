import { useState, useCallback, useEffect, createContext } from "react";
import AuthService from "../service/api/authService";

const AuthContext = createContext({
  accessToken: "",
  refreshToken: "",
  account: null,
  isLoading: true,
  login: (token) => {},
  logout: () => {},
  setTokens: (tokens) => {},
});

export const AuthContextProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [account, setAccount] = useState(null);
  const [profile, setProfile] = useState(null);
  const [accessToken, setAccessToken] = useState(null);
  const [refreshToken, setRefreshToken] = useState(null);

  useEffect(() => {
    const tokens = getTokens();

    if (tokens.accessToken) setAccessToken(tokens.accessToken);
    if (tokens.refreshToken) setAccessToken(tokens.refreshToken);

    if (tokens.accessToken) loadProfile();
    else setIsLoading(false);
  }, []);

  const login = async (account) => {
    setAccount(account);
    setAccessToken(account.accessToken);
    setRefreshToken(account.refreshToken);
    await loadProfile();
  };

  const setTokens = (accessToken, refreshToken) => {
    localStorage.setItem("accessToken", JSON.stringify(accessToken));
    localStorage.setItem("refreshToken", JSON.stringify(refreshToken));
  };

  const getTokens = () => {
    return {
      accessToken: localStorage.getItem("accessToken")
        ? JSON.parse(localStorage.getItem("accessToken"))
        : null,
      refreshToken: localStorage.getItem("refreshToken")
        ? JSON.parse(localStorage.getItem("refreshToken"))
        : null,
    };
  };

  const loadProfile = async () => {
    try {
      const response = await AuthService.getProfile();
      setProfile(response?.data?.data);
      setIsLoading(false);
    } catch (e) {
      console.log(e);
      // todo: check if access token is expired call renewToken()
      console.log("error occured!");
      setIsLoading(false);
    }
  };

  const renewToken = () => {
    logout();
    //todo: should get an api for refreshing tokens
  };

  const logout = useCallback(() => {
    localStorage.clear();
    window.location.reload();
  }, []);

  const contextValue = {
    isLoading,
    account,
    profile,
    accessToken,
    refreshToken,
    setTokens,
    getTokens,
    renewToken,
    loadProfile,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

export default AuthContext;
