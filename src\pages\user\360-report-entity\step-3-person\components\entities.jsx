import { SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import DropDown from "components/ui/DropDown";
import CLUSTER_COLORS from "constants/colors";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";
import ReactWordcloud from "react-wordcloud";
import { useReport360Store } from "store/report360Store";
import { toPersianNumber } from "utils/helper";
import Title from "pages/user/compare-create/components/step-two/charts/Title";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import use360requestStore from "store/360requestStore";
import usePlatformDataStore from "store/person360platform";
import ExportMenu from "components/ExportMenu/index.jsx";
import Drawer from "components/Drawer";
import WordContent from "pages/user/word-content";

const PersonEntities = ({ activePlatform, isUpdate }) => {
  const [loading, setLoading] = useState(false);
  const [wordCloudData, setWordCloudData] = useState([]);
  const [isDateChange, setIsDateChange] = useState(false);
  const [showWord, setShowWord] = useState(false);
  const [word, setWord] = useState("");
  const [activeTab, setActiveTab] = useState("اشخاص");
  const [activeTabName, setActiveTabName] = useState("person");
  const sourceReport = use360requestStore((state) => state.report);
  const report = use360requestStore((state) => state.report);
  const [frequentEntities, setFrequentEntities] = useState({
    person: [],
    location: [],
    event: [],
    organ: [],
  });
  const locationEntityCharts = use360requestStore(
    (state) => state.locationEntityCharts
  );
  const { type } = useReport360Store((state) => state.report);
  const { date, entity, setEntitiesData } = useReport360Store((state) => ({
    date: state.report.date,
    entity: state.report.entity,
    wordCloudData: state.wordCloudData,
    setEntitiesData: state.setEntitiesData,
  }));

  // Access Zustand store for caching
  const { platformData, setPlatformData, clearPlatformData } =
    usePlatformDataStore((state) => ({
      platformData: state.platformData[activePlatform]?.entities,
      setPlatformData: state.setPlatformData,
      clearPlatformData: state.clearPlatformData,
    }));

  const reportType =
    type === "topic" ? "entity" : type === "location" ? "location" : null;

  const chartData = report[reportType]?.[activePlatform]?.frequent_entities;

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Entity Count",
      data: wordCloudData.map((item) => item.value),
      time: wordCloudData.map((item) => item.text),
    },
  ];

  const time = wordCloudData.map((item) => item.text);

  const getData = async () => {
    const defaultFrequentEntities = {
      organ: [],
      person: [],
      location: [],
      event: [],
      politic_group: null,
      nationality: null,
    };

    // Use cached data if available and not updating
    if (
      !isUpdate &&
      platformData &&
      Object.keys(platformData).length > 0 &&
      !isDateChange
    ) {
      setFrequentEntities(platformData);
      const wordCloudFormattedData = (platformData[activeTabName] || []).map(
        ({ key, count }) => ({
          text: key,
          value: count,
        })
      );
      setWordCloudData(wordCloudFormattedData);
      setEntitiesData(activeTabName, wordCloudFormattedData);
      setLoading(false);
      return;
    }

    // Use chartData if updating and available
    if (
      isUpdate &&
      chartData &&
      Object.keys(chartData).length > 0 &&
      chartData !== defaultFrequentEntities &&
      !isDateChange
    ) {
      setFrequentEntities(chartData);
      setPlatformData(activePlatform, "entities", chartData); // Cache data
      const wordCloudFormattedData = (chartData[activeTabName] || []).map(
        ({ key, count }) => ({
          text: key,
          value: count,
        })
      );
      setWordCloudData(wordCloudFormattedData);
      setEntitiesData(activeTabName, wordCloudFormattedData);
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      const requestData = buildRequestData(
        {
          date,
          q: entity,
          platform: activePlatform,
        },
        "cloud",
        25
      );

      const res = await advanceSearch.search(requestData, null, {
        cloud_type: "entities",
      });

      const platformData = res?.data?.data?.[activePlatform] || {};

      const updatedEntities = {
        person: platformData.person || [],
        location: platformData.location || [],
        event: platformData.event || [],
        organ: platformData.organ || [],
      };

      setFrequentEntities(updatedEntities);
      setPlatformData(activePlatform, "entities", updatedEntities); // Cache data

      const arabicRange = /[\u0600-\u06FF]/;

      const updatedWordClouds = (updatedEntities[activeTabName] || []).map(
        ({ key, count }) => {
          let formattedKey = key;
          if (!arabicRange.test(key)) {
            if (key?.startsWith("#")) {
              formattedKey = key.slice(1) + "#";
            }
          }
          return { text: formattedKey, value: count };
        }
      );

      setEntitiesData(activeTabName, updatedWordClouds);
      setWordCloudData(updatedWordClouds);
    } catch (error) {
      console.error(error);
      setEntitiesData(activeTabName, []);
      setWordCloudData([]);
      setFrequentEntities(defaultFrequentEntities);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (Object.values(frequentEntities).some((arr) => arr?.length > 0)) {
      locationEntityCharts(
        frequentEntities,
        "frequent_entities",
        activePlatform,
        reportType
      );
    }
  }, [frequentEntities, reportType]);

  useEffect(() => {
    getData();
  }, [entity, date, isUpdate]);

  useEffect(() => {
    setIsDateChange(true);
    if (isUpdate || !date) {
      const entitiesData =
        sourceReport[reportType]?.[activePlatform]?.frequent_entities;

      if (entitiesData && Object.keys(entitiesData).length > 0) {
        setFrequentEntities({
          person: entitiesData.person || [],
          location: entitiesData.location || [],
          event: entitiesData.event || [],
          organ: entitiesData.organ || [],
        });
        setPlatformData(activePlatform, "entities", entitiesData); // Cache data
        const wordCloudFormattedData = (entitiesData[activeTabName] || []).map(
          ({ key, count }) => ({
            text: key,
            value: count,
          })
        );
        setWordCloudData(wordCloudFormattedData);
        setEntitiesData(activeTabName, wordCloudFormattedData);
      }
    }
  }, [isUpdate, date]);

  useEffect(() => {
    const platformData = frequentEntities[activeTabName] || [];

    if (Array.isArray(platformData)) {
      const wordCloudFormattedData = platformData.map(({ key, count }) => ({
        text: key,
        value: count,
      }));
      setWordCloudData(wordCloudFormattedData);
      setEntitiesData(activeTabName, wordCloudFormattedData);
    } else {
      console.warn(`Expected an array, but got: ${typeof platformData}`);
      setWordCloudData([]);
      setEntitiesData(activeTabName, []);
    }
  }, [activeTabName, frequentEntities]);

  const handleWordClick = (clickedWord) => {
    setWord(clickedWord.text);
    setShowWord(true);
  };

  const tabs = [
    { id: "event", title: "رویداد" },
    { id: "location", title: "مکان ها" },
    { id: "organ", title: "سازمان ها" },
    { id: "person", title: "اشخاص" },
  ];

  return (
    <>
      <Card className="px-0 !h-full card-animation card-delay">
        <div className="w-full flex flex-col gap-6">
          <div className="flex flex-row items-center gap-5 w-full justify-between">
            <div className="flex items-center w-full">
              <Title title="موجودیت های پرتکرار"></Title>
              <ExportMenu
                chartSelector=".person-entities-container"
                fileName="person-entities"
                series={series}
                time={time}
                excelHeaders={["Entity", "Count"]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
              />
            </div>
            <div className="tabs-360 gap-1 px-2 w-3/6 font-overline-medium">
              {tabs.map((tab, i) => (
                <span
                  key={i}
                  onClick={() => {
                    setActiveTabName(tab.id);
                    setActiveTab(tab.title);
                  }}
                  className={`duration-200 text-center !w-[8rem] cursor-pointer p-2 rounded ${
                    activeTab === tab.title
                      ? "text-[#6F5CD1] bg-[#E9E6F7]"
                      : "hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
                  }`}
                >
                  {tab.title}
                </span>
              ))}
            </div>

            <div className="dropdown-360 w-2/5">
              <DropDown
                title="انتخاب موجودیت"
                subsets={tabs.map((tab) => tab.title)}
                selected={activeTab}
                setSelected={(value) => {
                  const selectedTab = tabs.find((tab) => tab.title === value);
                  if (selectedTab) {
                    setActiveTabName(selectedTab.id);
                    setActiveTab(value);
                  }
                }}
              />
            </div>
          </div>

          {loading && (
            <div className="flex w-full h-full! justify-center items-center">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          )}

          {!loading && wordCloudData.length === 0 && (
            <div className="h-full flex items-center justify-center font-subtitle-medium pt-40">
              داده ای برای نمایش وجود ندارد
            </div>
          )}

          <div className="grid grid-cols-1 h-full">
            <div className="flex flex-1 responsive-svg h-full">
              <ReactWordcloud
                options={{
                  rotations: 1,
                  rotationAngles: [0],
                  enableTooltip: true,
                  deterministic: false,
                  fontFamily: "iranyekan",
                  fontSizes: [14, 54],
                  padding: 10,
                  colors: CLUSTER_COLORS,
                  tooltipOptions: { theme: "light", arrow: true },
                }}
                words={wordCloudData}
                callbacks={{
                  getWordTooltip: (word) => {
                    const countInPersian = toPersianNumber(word?.value);
                    return `${word.text} (${countInPersian})`;
                  },
                  onWordClick: handleWordClick,
                }}
              />
            </div>
          </div>
        </div>
      </Card>
      {showWord && (
        <Drawer setShowMore={setShowWord}>
          <WordContent word={word} />
        </Drawer>
      )}
    </>
  );
};

export default PersonEntities;

PersonEntities.propTypes = {
  activePlatform: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool,
};
