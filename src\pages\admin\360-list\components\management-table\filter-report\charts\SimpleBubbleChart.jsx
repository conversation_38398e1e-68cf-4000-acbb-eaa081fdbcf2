import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";

const SimpleBubbleChart = ({ data }) => {
  const options = {
    chart: {
      type: "bubble",
      plotBorderWidth: 0,
      borderWidth: 0,
      zooming: {
        enabled: false,
      },
      height: "35%",
    },
    title: {
      text: null,
    },
    credits: {
      enabled: false,
    },
    xAxis: {
      lineWidth: 1,
      minorGridLineWidth: 0,
      lineColor: "#949EB7",
      labels: {
        enabled: true,
        // Format Y-axis labels
        formatter: function () {
          return `${toPersianNumber(this.value)} accounts`; // Example: Add "accounts" after the number
        },
        style: {
          fontSize: "12px", // Customize font size
          fontWeight: "light", // Customize font weight
          color: "#949EB7", // Change label color
        },
      },
      minorTickLength: 0,
      tickLength: 0,
      gridLineWidth: 0,

      title: {
        text: "تاریخ ایجاد حساب",
      },
    },
    yAxis: {
      lineWidth: 1,
      minorGridLineWidth: 0,
      lineColor: "#949EB7",
      labels: {
        enabled: false,
      },
      minorTickLength: 0,
      tickLength: 0,
      gridLineWidth: 0,

      startOnTick: false,
      endOnTick: false,
      title: {
        text: "تعدا اکانت ساخته شده",
      },
    },
    legend: {
      enabled: false,
    },
    tooltip: {
      headerFormat: "<b>{series.name}</b><br>",
      pointFormat: "X: {point.x}, Y: {point.y}, Size: {point.z}",
    },
    plotOptions: {
      bubble: {
        minSize: "5%",
        maxSize: "20%",
        dataLabels: {
          enabled: true,
          format: "{point.name}", // Displays the name property from each data point
          style: {
            fontSize: "10px",
            color: "black",
            textOutline: "none",
          },
        },
      },
    },
    series: [
      {
        name: "Sample Bubbles",
        color: "#4D36BF",
        data: data, // Format: [[x, y, size], ...]
      },
    ],
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default SimpleBubbleChart;
