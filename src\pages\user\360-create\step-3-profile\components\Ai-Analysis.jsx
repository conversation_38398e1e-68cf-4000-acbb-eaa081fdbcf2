import { StarFour } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import "../../style.css";
import use360requestStore from "store/360requestStore";

const AiAnalysis = () => {
  const sourceReport = use360requestStore((state) => state.report);

  return (
    <Card className="flex flex-col gap-2 card-animation card-delay h-full">
      <h3 className="font-subtitle-large flex items-center gap-2">
        <StarFour />
        تحلیل منبع از نگاه هوش مصنوعی
      </h3>
      <p className="font-body-small text-[#8f8f8f]">
        این محتوا توسط هوش مصنوعی بر اساس فعالیت‌های این اکانت تولید شده است
      </p>
      {sourceReport?.content?.source_info?.ai_summary ? (
        <p className="font-paragraph-large py-3 text-justify">
          {sourceReport?.content?.source_info?.ai_summary || ""}
        </p>
      ) : (
        <div className="h-60 flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      )}
    </Card>
  );
};

export default AiAnalysis;
