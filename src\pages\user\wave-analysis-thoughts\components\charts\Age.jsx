import Bar from "components/Charts/Bar";
import { toPersianNumber } from "utils/helper";
import PropTypes from "prop-types";

const Age = ({ legend = true, width = 350, agesData = [] }) => {
  const sortedAgesData = agesData
    .filter(({ key }) => key !== "unknown")
    .sort((a, b) => b.count - a.count);

  const dataObject = sortedAgesData.reduce((acc, { key, count }) => {
    acc[key] = count;
    return acc;
  }, {});

  const totalCount = sortedAgesData.reduce((sum, { count }) => sum + count, 0);

  const chartData = {
    teenager: dataObject.teenager || 0,
    young: dataObject.young || 0,
    middle_aged: dataObject.middle_aged || 0,
    elderly: dataObject.elderly || 0,
    unknown: dataObject.unknown || 0,
  };

  const percentages = {
    teenager: totalCount ? (chartData.teenager / totalCount) * 100 : 0,
    young: totalCount ? (chartData.young / totalCount) * 100 : 0,
    middle_aged: totalCount ? (chartData.middle_aged / totalCount) * 100 : 0,
    elderly: totalCount ? (chartData.elderly / totalCount) * 100 : 0,
  };

  // Define sorted categories for consistent rendering
  const categories = [
    {
      key: "teenager",
      label: "نوجوان",
      icon: "/icons/teenager-small.svg",
      color: "#43B3E4",
    },
    {
      key: "young",
      label: "جوان",
      icon: "/icons/young-small.svg",
      color: "#2A7DE0",
    },
    {
      key: "middle_aged",
      label: "میانسال",
      icon: "/icons/middleage-small.svg",
      color: "#3144EF",
    },
    {
      key: "elderly",
      label: "سالخورده",
      icon: "/icons/old-small.svg",
      color: "#5911CF",
    },
  ].sort((a, b) => (chartData[b.key] || 0) - (chartData[a.key] || 0));

  return (
    <div className="flex flex-col gap-4 justify-between">
      <Bar width={width} data={chartData} />
      {legend && (
        <div className="flex flex-col gap-2 *:flex *:justify-between">
          {categories.map(({ key, label, icon, color }) => (
            <div key={key}>
              <div className={`font-body-medium text-[${color}] flex gap-1`}>
                <img src={icon} alt={`${label} icon`} />
                <p>{label}</p>
              </div>
              <div className="font-body-medium">
                {toPersianNumber(String(percentages[key].toFixed(1)))}%
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

Age.propTypes = {
  legend: PropTypes.bool,
  width: PropTypes.number,
  agesData: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string,
      count: PropTypes.number,
    })
  ),
};

export default Age;
