import PropTypes from "prop-types";
import {
  AddressBook,
  CodesandboxLogo,
  Eyeglasses,
  FileText,
} from "@phosphor-icons/react";
import "../../style.css";
import { useReport360Store } from "store/report360Store";
import ToolTip from "components/ui/ToolTip";

const ReportsType = ({
  activeTab,
  setIsFixed,
  setActiveSpan,
  activeSpan,
  isFixed,
}) => {
  const { profile } = useReport360Store((state) => state.report);
  const platform = profile.platform;

  const tabs = [
    {
      name: "overview",
      label: "نمای کلی منبع",
      icon: <Eyeglasses />,
      desc: "در این قسمت، اطلاعات کلی منبع را در بستر انتخاب شده مشاهده می‌کنید.",
    },
    platform !== "telegram" &&
      platform !== "news" && {
        name: "contacts",
        label: "اطلاعات مخاطبان",
        icon: <AddressBook />,
        desc: "در این قسمت، داده‌های فالوور ها مورد بررسی قرار می‌گیرد.",
        disabled: true,
      },
    platform !== "news" && {
      name: "analysis",
      label: "تحلیل افکار و عقاید",
      icon: <CodesandboxLogo />,
      desc: "در این قسمت، بر اساس بررسی داده‌ها توسط هوش مصنوعی، گرایشات فکری و عقاید منبع مورد نظر نمایش داده می‌شود.",
      disabled: platform === "telegram",
    },
    {
      name: "source-content",
      label: "محتوای منبع",
      icon: <FileText />,
      desc: "در این قسمت، محتوای مرتبط با منبع مورد نظر را بر اساس تاریخ فیلتر شده مشاهده می‌کنید.",
    },
  ].filter(Boolean);

  const handleSpanClick = (spanName, disabled) => {
    if (disabled) return;
    setIsFixed(false);
    setActiveSpan(spanName);
  };

  const getSpanClass = (spanName, disabled) =>
    `flex items-center gap-2 duration-200 p-2 rounded-lg cursor-pointer relative font-body-medium ${
      disabled
        ? "text-gray-400 cursor-not-allowed"
        : activeTab === spanName
        ? "text-[#6F5CD1] after:content-[''] after:absolute after:bottom-[-1px] after:left-0 after:w-full after:h-[2px] after:bg-[#6F5CD1]"
        : "hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
    }`;

  return (
    <div
      className={
        activeSpan !== "source-content" && isFixed ? `fixed w-[85%] z-10` : ""
      }
    >
      <div className="font-body-small gap-3 flex pb-1 border-b border-gray-200 relative">
        {tabs.map((tab, index) =>
          !isFixed ? (
            <ToolTip
              key={index}
              comp={tab.desc}
              position="top"
              className="font-bold"
            >
              <span
                className={getSpanClass(tab.name, tab.disabled)}
                onClick={() => handleSpanClick(tab.name, tab.disabled)}
              >
                {tab.icon}
                {tab.label}
              </span>
            </ToolTip>
          ) : (
            <span
              className={getSpanClass(tab.name, tab.disabled)}
              onClick={() => {
                handleSpanClick(tab.name, tab.disabled);
                if (isFixed) {
                  () => setIsFixed(false);
                }
              }}
              key={index}
            >
              {tab.icon}
              {tab.label}
            </span>
          )
        )}
      </div>
    </div>
  );
};

export default ReportsType;

ReportsType.propTypes = {
  activeTab: PropTypes.string.isRequired,
  setActiveSpan: PropTypes.func.isRequired,
  setIsFixed: PropTypes.func.isRequired,
};
