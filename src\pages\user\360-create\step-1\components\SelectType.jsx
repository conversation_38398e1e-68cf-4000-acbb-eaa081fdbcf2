import { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import clsx from "clsx";

const SelectType = ({ handleChange = () => {}, nextStep }) => {
  const [type, setType] = useState("");
  const lastClickTime = useRef(0);

  useEffect(() => {
    handleChange(type);
  }, [type]);

  const handleDoubleClick = () => {
    const currentTime = new Date().getTime();
    if (currentTime - lastClickTime.current < 300) {
      nextStep();
    }
    lastClickTime.current = currentTime;
  };

  const handleClick = (selectedType) => {
    setType(selectedType);
    handleDoubleClick(selectedType);
  };

  return (
    <div className="flex flex-col gap-4">
      <span className="font-subtitle-large">نوع گزارش را انتخاب کنید</span>
      <div className="flex flex-col gap-4">
        <div className="flex gap-4">
          <div
            className={clsx(
              "flex flex-col items-center gap-4 w-[280px] h-[248px] p-2 text-center border rounded-lg cursor-pointer",
              type === "profile"
                ? "border-light-primary-border-rest bg-light-primary-background-highlight"
                : "border-light-neutral-border-low-rest"
            )}
            onClick={() => handleClick("profile")}
          >
            <img src="/360reports/Social.png" alt="compare subject" />
            <span className={"text-body-bold-medium"}>گزارش استعلام منبع</span>
            <span className="font-body-small text-light-neutral-text-medium">
              در این گزارش شما <strong>اکانت</strong> یک فرد را جست‌وجو کرده و
              اطلاعات مربوط به حساب کاربری او را خواهید دید
            </span>
          </div>
          <div
            className={clsx(
              "flex flex-col items-center gap-4 w-[280px] h-[248px] p-2 text-center border rounded-lg cursor-pointer",
              type === "topic"
                ? "border-light-primary-border-rest bg-light-primary-background-highlight"
                : "border-light-neutral-border-low-rest"
            )}
            onClick={() => handleClick("topic")}
          >
            <img src="/360reports/Person.png" alt="compare profile" />
            <span>
              گزارش استعلام موجودیت{" "}
              {/* <span
                className={
                  "rounded bg-light-inform-background-highlight text-light-inform-text-rest font-body-small px-3 py-[2px] mr-3"
                }
              >
                به زودی
              </span> */}
            </span>
            <span className="font-body-small text-light-neutral-text-medium">
              در این گزارش نام یک{" "}
              <strong>فرد، سازمان، حزب، تیم ورزشی و ...</strong> را جست‌وجو کرده
              و اطلاعات تمام شبکه‌های اجتماعی آن را خواهید دید
            </span>
          </div>
          <div
            className={clsx(
              "flex flex-col items-center gap-4 w-[280px] h-[248px] p-2 text-center border rounded-lg cursor-pointer",
              type === "location"
                ? "border-light-primary-border-rest bg-light-primary-background-highlight"
                : "border-light-neutral-border-low-rest"
            )}
            onClick={() => handleClick("location")}
          >
            <img src="/360reports/Location.png" alt="compare profile" />
            <span>
              گزارش استعلام مکان محور
              {/* <span
                className={
                  "rounded bg-light-inform-background-highlight text-light-inform-text-rest font-body-small px-3 py-[2px] mr-3"
                }
              >
                به زودی
              </span> */}
            </span>
            <span className="font-body-small text-light-neutral-text-medium">
              در این گزارش شما می‌توانید در مورد یک <strong>مکان</strong>{" "}
              جست‌و‌جو کنید
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

SelectType.propTypes = {
  handleChange: PropTypes.func,
  nextStep: PropTypes.func.isRequired,
};

export default SelectType;
