import { useCallback, useEffect, useRef, useState, memo } from "react";
import advanceSearch from "service/api/advanceSearch.js";
import { useReport360Store } from "store/report360Store";
import Paginate from "components/ui/Paginate";
import { findPlatform } from "pages/user/hot-topic/utils";
import SummaryCard360 from "./components/SummaryCard360";
import Loading from "components/ui/Loading";
import { buildRequestData } from "utils/requestData";
import { DownloadExcelButton } from "components/DownloadExcelButton";
import SORT_TYPE from "constants/sort-type";
import DropDown from "components/ui/DropDown";
import use360requestStore from "store/360requestStore";

const SourceContent = () => {
  const { date } = useReport360Store((state) => state.report);
  const sourceReport = use360requestStore((state) => ({
    platform: state.report?.content?.report_platform,
    profile: state.report?.content?.source_info,
  }));
  const platform = sourceReport.platform;
  const profile = sourceReport.profile;
  const sourceId =
    profile.user_name ||
    profile.source_name ||
    profile.channel_id ||
    profile.id;

  const { from, to } = date;
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [sort, setSort] = useState({ fa: "زمان انتشار", en: "date" });
  const [dataArray, setDataArray] = useState([]);
  const [dataCount, setDataCount] = useState(0);

  const countLoadingOffset = useRef();

  const handleQuery = useCallback(() => {
    return buildRequestData({
      page,
      sort: sort.en,
      sources: [sourceId.toString()],
      platform: platform,
      date: {
        from: from ? new Date(from) : undefined,
        to: to ? new Date(to) : undefined,
      },
    });
  }, [page, sort, platform, sourceId, from, to]);

  const getSearchData = async () => {
    if (loading) return;

    setLoading(true);
    const filters = {
      page,
      sort: sort.en,
      platform,
      date: {
        from: from ? new Date(from) : undefined,
        to: to ? new Date(to) : undefined,
      },
    };

    try {
      const filterObject = {
        ...filters,
        sources: [sourceId.toString()],
      };
      const req = buildRequestData(filterObject, "posts", 12);

      const responseStinas = await advanceSearch.search(req);
      const parsedResponse = responseStinas?.data?.data?.[platform];
      const total = responseStinas?.data?.data?.total;

      if (parsedResponse) {
        setDataArray(parsedResponse);
        setDataCount(total);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let debounceTimer;

    const fetchData = () => {
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        getSearchData();
      }, 100);
    };

    fetchData();

    return () => {
      clearTimeout(debounceTimer);
    };
  }, [handleQuery, date]);

  useEffect(() => {
    setPage(1);
  }, [sort]);

  return (
    <div className="flex flex-col">
      <div className="flex justify-end pl-6 items-center gap-2 mt-4">
        <div className="flex justify-end w-fit ml-2 backdrop-blur-md bg-white/20 rounded-lg">
          <DownloadExcelButton size="lg" platform={platform} q={handleQuery()}>
            دانلود گزارش اکسل
          </DownloadExcelButton>
        </div>

        <div className="border border-[#d6d6db] rounded-lg !bg-white">
          <DropDown
            title="نمایش بر اساس"
            subsets={SORT_TYPE[platform].map((item) => item.fa)}
            selected={sort.fa}
            setSelected={(value) => {
              setSort(
                SORT_TYPE[platform].filter((item) => item.fa === value)[0]
              );
            }}
            onOpenChange={(isOpen) => setIsDropdownOpen(isOpen)}
          />
        </div>
      </div>
      {loading ? (
        <Loading height={countLoadingOffset?.current?.clientHeight + 24} />
      ) : (
        <>
          <div className="mt-4 flex flex-wrap justify-between w-full">
            {dataArray?.map((data) => (
              <div
                className={`w-full ${
                  dataArray.length === 2 ? "md:w-1/2" : "md:w-1/3"
                }`}
                key={data.id}
              >
                <SummaryCard360
                  media={findPlatform(data)}
                  data={data}
                  platform={platform}
                  isDropdownOpen={isDropdownOpen}
                />
              </div>
            ))}
            <div className="w-full">
              <Paginate
                page={page}
                setPage={setPage}
                dataCount={dataCount}
                per_page={12}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default memo(SourceContent);
