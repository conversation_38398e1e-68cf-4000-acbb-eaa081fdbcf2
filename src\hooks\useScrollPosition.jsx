import {useEffect, useState} from "react";

const SCROLL_THRESHOLD_MIN = 90;
const SCROLL_THRESHOLD_MAX = 170;

export const useScrollPosition = () => {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    if (scrollPosition > SCROLL_THRESHOLD_MAX) {
      setIsScrolled(true);
    } else if (scrollPosition < SCROLL_THRESHOLD_MIN) {
      setIsScrolled(false);
    }
  }, [scrollPosition]);

  return {scrollPosition, isScrolled};
};