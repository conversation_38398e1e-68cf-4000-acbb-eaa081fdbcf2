import Divider from "components/ui/Divider";
import { toPersianNumber } from "utils/helper";
const legendConfig = [
  {
    id: 6,
    title: "حامیان انقلاب اسلامی",
    dataKey: "osoolgera",
    color: "#47DBB6",
  },
  {
    id: 2,
    title: "فعالین مدنی غرب‌گرا",
    dataKey: "eslahtalab",
    color: "#6F5CD1",
  },
  {
    id: 4,
    title: "سلطنت‌طلب",
    dataKey: "saltanat",
    color: "#DEC402",
  },
  {
    id: 3,
    title: "برانداز",
    dataKey: "barandaz",
    color: "#E0526A",
  },
  {
    id: 5,
    title: "عدالت‌خواه",
    dataKey: "edalatkhah",
    color: "#4EA1FA",
  },
  {
    id: 1,
    title: "منافقین",
    dataKey: "monafegh",
    color: "#dc6414",
  },
  {
    id: 0,
    title: "احمدی‌نژادی",
    dataKey: "ahmadinezhad",
    color: "#918c8c",
  },
  {
    id: 7,
    title: "ری‌استارتی",
    dataKey: "restart",
    color: "#A45E2C",
  },
  {
    id: 8,
    title: "ناشناخته",
    dataKey: "unknown",
    color: "#000",
  },
];

const PoliticalSpectrumContribution = ({ graphData }) => {
  const legends = legendConfig?.map((legend) => {
    const value = graphData?.[legend?.dataKey] || 0;
    const percent = ((value / graphData?.total) * 100).toFixed(1);
    return {
      ...legend,
      percent: `${percent}%`,
    };
  });
  if (!graphData || !graphData.total) {
    return (
      <div className="bg-light-neutral-surface-card rounded-[8px] px-6 py-9">
        <p className="font-subtitle-large pb-4">طیف‌های سیاسی مشارکت کننده</p>
        <Divider />
        <p className="font-body-medium text-center pt-6">در حال بارگذاری...</p>
      </div>
    );
  }
  return (
    <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
      <p className="font-subtitle-large pb-4">طیف‌های سیاسی مشارکت کننده</p>
      <Divider />
      <div className="flex items-center justify-center pt-6 gap-7">
        {legends?.map((legend) => (
          <div key={legend?.id}>
            <div className="flex items-center gap-1">
              <span
                style={{ backgroundColor: legend?.color }}
                className="w-3 h-3 rounded-full"
              ></span>
              <p
                style={{ color: legend?.color }}
                className="font-body-bold-large"
              >
                {legend?.title}
              </p>
            </div>
            <p className="font-body-medium text-center pt-1">
              {toPersianNumber(legend?.percent)}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PoliticalSpectrumContribution;
