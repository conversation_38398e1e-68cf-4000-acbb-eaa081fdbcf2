// import { CaretLeft } from "@phosphor-icons/react";
// import Popup from "components/ui/PopUp";
// import { memo, useEffect, useRef, useState, useMemo, useCallback } from "react";
// import ForceGraph2D from "react-force-graph-2d";
// import fallbackImage from "/logo_small.png";
// import advanceSearch from "service/api/advanceSearch";
// import { buildRequestData } from "utils/requestData";
// import TextWrapper from "components/TextWrapper";
// import Divider from "components/ui/Divider";
// import SentimentAnalysis from "pages/user/hot-topic/components/ShowMoreDetail/SentimentAnalysis";
// import TopicCommunication from "pages/user/hot-topic/components/ShowMoreDetail/TopicCommunication";
// import ListDrawer from "pages/user/360-create/step-2-profile/components/ProfileList/ListDrawer";
// import { Link } from "react-router-dom";
// import { useReport360Store } from "store/report360Store";

// const UserInfoPopup = memo(({ isOpen, userInfo, onClose }) => {
//   const setReport = useReport360Store((state) => state.setReport);
//   if (!userInfo) return null;
//   return (
//     <Popup
//       hasButton={false}
//       isOpen={isOpen}
//       onClose={onClose}
//       // hasCloseIcon={false}
//     >
//       <div className="flex flex-col gap-4">
//         <ListDrawer
//           data={userInfo?.twitter[0]}
//           media={"twitter"}
//           showMediaName
//           fallbackImg={fallbackImage}
//           showHeaderMenu={false}
//         >
//           <TextWrapper media={"twitter"}>
//             {userInfo?.content
//               ? userInfo?.content
//               : userInfo?.bio || userInfo?.twitter[0]?.bio}
//           </TextWrapper>
//         </ListDrawer>
//         <Divider />
//         {userInfo.sentiment_negative &&
//           userInfo.sentiment_positive &&
//           userInfo.sentiment_neutral && (
//             <>
//               <SentimentAnalysis data={userInfo} />
//               <Divider />
//             </>
//           )}
//         {userInfo.categories ? <TopicCommunication data={userInfo} /> : null}
//       </div>
//       <div className="flex justify-center items-center gap-1 text-light-primary-text-rest mt-5">
//         <Link
//           to={"/app/report-360/create"}
//           onClick={() =>
//             setReport({
//               step: 3,
//               // id: userInfo?.twitter[0]?.id,
//               type: "profile",
//               profile: {
//                 id: userInfo?.twitter[0]?.id,
//                 user_title: userInfo?.twitter[0]?.user_title,
//                 user_name: userInfo?.twitter[0]?.user_name,
//                 platform: "twitter",
//                 avatar: userInfo?.twitter[0]?.avatar,
//               },
//             })
//           }
//           className="flex justify-center items-center gap-1 duration-200 p-1 rounded-lg hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
//         >
//           <span className="font-overline-medium">مشاهده گزارش ۳۶۰</span>
//           <CaretLeft size={12} />
//         </Link>
//       </div>
//     </Popup>
//   );
// });

// const NetworkGraphChart = ({ fetchedData, selectedNode, from, to }) => {
//   const [userInfo, setUserInfo] = useState(null);
//   const [isPopupOpen, setIsPopupOpen] = useState(false);
//   const [nodesCount, setNodesCount] = useState(0);
//   const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
//   const fgRef = useRef();
//   const containerRef = useRef();

//   const handleNodeClick = useCallback(
//     async (node) => {
//       setIsPopupOpen(true);
//       try {
//         const filters = {
//           date: { from, to },
//           q: node?.node_name,
//         };
//         const infoQuery = buildRequestData(filters, "search_in_source");
//         const response = await advanceSearch.search(infoQuery);
//         setUserInfo(response?.data?.data);
//       } catch (error) {
//         console.error("Failed to fetch user info:", error);
//         setUserInfo(node);
//       }
//     },
//     [from, to]
//   );

//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const { width, height } = containerRef.current.getBoundingClientRect();
//         setDimensions({ width, height });
//       }
//     };
//     handleResize();
//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   useEffect(() => {
//     if (fetchedData?.nodes_count) {
//       setNodesCount(fetchedData.nodes_count);
//     }
//   }, [fetchedData]);

//   const graphData = useMemo(
//     () => ({
//       nodes: fetchedData?.nodes || [],
//       links: fetchedData?.links || [],
//     }),
//     [fetchedData]
//   );

//   const getNodeColor = (node) => {
//     return selectedNode && node.node_name === selectedNode.node_name
//       ? "#4362B3"
//       : "#9E8DF3";
//   };

//   // Compute connected nodes (first and second-degree neighbors)
//   const connectedNodes = useMemo(() => {
//     if (!selectedNode || !graphData.links)
//       return { firstDegree: new Set(), secondDegree: new Set() };

//     const firstDegree = new Set();
//     const secondDegree = new Set();

//     // Find first-degree neighbors
//     graphData.links.forEach((link) => {
//       if (link.source.node_name === selectedNode.node_name) {
//         firstDegree.add(link.target.node_name);
//       } else if (link.target.node_name === selectedNode.node_name) {
//         firstDegree.add(link.source.node_name);
//       }
//     });

//     // Find second-degree neighbors
//     graphData.links.forEach((link) => {
//       if (
//         firstDegree.has(link.source.node_name) &&
//         link.target.node_name !== selectedNode.node_name &&
//         !firstDegree.has(link.target.node_name)
//       ) {
//         secondDegree.add(link.target.node_name);
//       } else if (
//         firstDegree.has(link.target.node_name) &&
//         link.source.node_name !== selectedNode.node_name &&
//         !firstDegree.has(link.source.node_name)
//       ) {
//         secondDegree.add(link.source.node_name);
//       }
//     });

//     return { firstDegree, secondDegree };
//   }, [selectedNode, graphData]);

//   return (
//     <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
//       <ForceGraph2D
//         ref={fgRef}
//         graphData={graphData}
//         linkColor={() => "#BEC4C5"}
//         nodeId="node_name"
//         dagLevelDistance={10}
//         nodeRelSize={10}
//         width={dimensions.width}
//         height={dimensions.height}
//         maxZoom={10}
//         minZoom={0.05}
//         nodeLabel="node_name"
//         onNodeClick={handleNodeClick}
//         nodeCanvasObject={(node, ctx, globalScale) => {
//           const label = "";
//           const isSelected =
//             selectedNode && node.node_name === selectedNode.node_name;
//           const isFirstDegree = connectedNodes.firstDegree.has(node.node_name);
//           const isSecondDegree = connectedNodes.secondDegree.has(
//             node.node_name
//           );
//           const radius = 5;
//           // Draw halo based on node type
//           if (isSelected) {
//             // Halo for selected node
//             // ctx.beginPath();
//             // ctx.arc(node.x, node.y, radius + 15, 0, 2 * Math.PI, false);
//             // ctx.fillStyle = "rgba(255, 0, 0, 0.4)"; // Bright red, semi-transparent
//             // ctx.fill();
//           } else if (isFirstDegree) {
//             // Halo for first-degree neighbors
//             ctx.beginPath();
//             ctx.arc(node.x, node.y, radius + 6, 0, 2 * Math.PI, false);
//             ctx.fillStyle = "#4362B3"; // Orange, less opaque
//             ctx.fill();
//           } else if (isSecondDegree) {
//             // Halo for second-degree neighbors
//             // ctx.beginPath();
//             // ctx.arc(node.x, node.y, radius + 4, 0, 2 * Math.PI, false);
//             // ctx.fillStyle = "#4ceb34"; // Yellow, faint
//             // ctx.fill();
//           }

//           // Draw the node itself
//           ctx.beginPath();
//           ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI, false);
//           ctx.fillStyle = getNodeColor(node);
//           ctx.fill();

//           // Draw label
//           const fontSize = 12 / globalScale;
//           ctx.font = `${fontSize}px Sans-Serif`;
//           const textWidth = ctx.measureText(label).width;
//           const bckgDimensions = [textWidth, fontSize].map(
//             (n) => n + fontSize * 0.2
//           );

//           if (globalScale > 3) {
//             ctx.fillStyle = "#000000";
//             ctx.fillText(label, node.x + 8, node.y + 3);
//           }
//         }}
//       />
//       <UserInfoPopup
//         isOpen={isPopupOpen}
//         userInfo={userInfo}
//         nodesCount={nodesCount}
//         onClose={() => setIsPopupOpen(false)}
//       />
//     </div>
//   );
// };

// export default memo(NetworkGraphChart);

// import { CaretLeft } from "@phosphor-icons/react";
// import Popup from "components/ui/PopUp";
// import { memo, useEffect, useRef, useState, useMemo, useCallback } from "react";
// import ForceGraph2D from "react-force-graph-2d";
// import fallbackImage from "/logo_small.png";
// import advanceSearch from "service/api/advanceSearch";
// import { buildRequestData } from "utils/requestData";
// import TextWrapper from "components/TextWrapper";
// import Divider from "components/ui/Divider";
// import SentimentAnalysis from "pages/user/hot-topic/components/ShowMoreDetail/SentimentAnalysis";
// import TopicCommunication from "pages/user/hot-topic/components/ShowMoreDetail/TopicCommunication";
// import ListDrawer from "pages/user/360-create/step-2-profile/components/ProfileList/ListDrawer";
// import { Link } from "react-router-dom";
// import { useReport360Store } from "store/report360Store";

// const NetworkGraphChart = ({ fetchedData, selectedNode, from, to }) => {
//   const [userInfo, setUserInfo] = useState(null);
//   const [isPopupOpen, setIsPopupOpen] = useState(false);
//   const [nodesCount, setNodesCount] = useState(0);
//   const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
//   const fgRef = useRef();
//   const containerRef = useRef();
//   const setReport = useReport360Store((state) => state.setReport);

//   const handleNodeClick = useCallback(
//     async (node) => {
//       setIsPopupOpen(true);
//       try {
//         const filters = {
//           date: { from, to },
//           q: node?.node_name,
//         };
//         const infoQuery = buildRequestData(filters, "search_in_source");
//         const response = await advanceSearch.search(infoQuery);
//         setUserInfo(response?.data?.data);
//       } catch (error) {
//         console.error("Failed to fetch user info:", error);
//         setUserInfo(node);
//       }
//     },
//     [from, to]
//   );

//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const { width, height } = containerRef.current.getBoundingClientRect();
//         setDimensions({ width, height });
//       }
//     };
//     handleResize();
//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   useEffect(() => {
//     if (fetchedData?.nodes_count) {
//       setNodesCount(fetchedData.nodes_count);
//     }
//   }, [fetchedData]);

//   const graphData = useMemo(
//     () => ({
//       nodes: fetchedData?.nodes || [],
//       links: fetchedData?.links || [],
//     }),
//     [fetchedData]
//   );

//   const getNodeColor = (node) => {
//     return selectedNode && node.node_name === selectedNode.node_name
//       ? "#4362B3"
//       : "#9E8DF3";
//   };

//   const connectedNodes = useMemo(() => {
//     if (!selectedNode || !graphData.links)
//       return { firstDegree: new Set(), secondDegree: new Set() };

//     const firstDegree = new Set();
//     const secondDegree = new Set();

//     graphData.links.forEach((link) => {
//       if (link.source.node_name === selectedNode.node_name) {
//         firstDegree.add(link.target.node_name);
//       } else if (link.target.node_name === selectedNode.node_name) {
//         firstDegree.add(link.source.node_name);
//       }
//     });

//     graphData.links.forEach((link) => {
//       if (
//         firstDegree.has(link.source.node_name) &&
//         link.target.node_name !== selectedNode.node_name &&
//         !firstDegree.has(link.target.node_name)
//       ) {
//         secondDegree.add(link.target.node_name);
//       } else if (
//         firstDegree.has(link.target.node_name) &&
//         link.source.node_name !== selectedNode.node_name &&
//         !firstDegree.has(link.source.node_name)
//       ) {
//         secondDegree.add(link.source.node_name);
//       }
//     });

//     return { firstDegree, secondDegree };
//   }, [selectedNode, graphData]);

//   return (
//     <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
//       <ForceGraph2D
//         ref={fgRef}
//         graphData={graphData}
//         linkColor={() => "#BEC4C5"}
//         nodeId="node_name"
//         dagLevelDistance={10}
//         nodeRelSize={10}
//         width={dimensions.width}
//         height={dimensions.height}
//         maxZoom={10}
//         minZoom={0.05}
//         nodeLabel="node_name"
//         onNodeClick={handleNodeClick}
//         nodeCanvasObject={(node, ctx, globalScale) => {
//           const label = "";
//           const isSelected =
//             selectedNode && node.node_name === selectedNode.node_name;
//           const isFirstDegree = connectedNodes.firstDegree.has(node.node_name);
//           const isSecondDegree = connectedNodes.secondDegree.has(
//             node.node_name
//           );
//           const radius = 5;

//           if (isFirstDegree) {
//             ctx.beginPath();
//             ctx.arc(node.x, node.y, radius + 6, 0, 2 * Math.PI, false);
//             ctx.fillStyle = "#4362B3";
//             ctx.fill();
//           }

//           ctx.beginPath();
//           ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI, false);
//           ctx.fillStyle = getNodeColor(node);
//           ctx.fill();

//           const fontSize = 12 / globalScale;
//           ctx.font = `${fontSize}px Sans-Serif`;
//           const textWidth = ctx.measureText(label).width;
//           const bckgDimensions = [textWidth, fontSize].map(
//             (n) => n + fontSize * 0.2
//           );

//           if (globalScale > 3) {
//             ctx.fillStyle = "#000000";
//             ctx.fillText(label, node.x + 8, node.y + 3);
//           }
//         }}
//       />
//       {userInfo && (
//         <Popup
//           hasButton={false}
//           isOpen={isPopupOpen}
//           onClose={() => setIsPopupOpen(false)}
//         >
//           <div className="flex flex-col gap-4">
//             <ListDrawer
//               data={userInfo?.twitter[0]}
//               media={"twitter"}
//               showMediaName
//               fallbackImg={fallbackImage}
//               showHeaderMenu={false}
//             >
//               <TextWrapper media={"twitter"}>
//                 {userInfo?.content
//                   ? userInfo?.content
//                   : userInfo?.bio || userInfo?.twitter[0]?.bio}
//               </TextWrapper>
//             </ListDrawer>
//             <Divider />
//             {userInfo.sentiment_negative &&
//               userInfo.sentiment_positive &&
//               userInfo.sentiment_neutral && (
//                 <>
//                   <SentimentAnalysis data={userInfo} />
//                   <Divider />
//                 </>
//               )}
//             {userInfo.categories ? (
//               <TopicCommunication data={userInfo} />
//             ) : null}
//           </div>
//           <div className="flex justify-center items-center gap-1 text-light-primary-text-rest mt-5">
//             <Link
//               to={"/app/report-360/create"}
//               onClick={() =>
//                 setReport({
//                   step: 3,
//                   type: "profile",
//                   profile: {
//                     id: userInfo?.twitter[0]?.id,
//                     user_title: userInfo?.twitter[0]?.user_title,
//                     user_name: userInfo?.twitter[0]?.user_name,
//                     platform: "twitter",
//                     avatar: userInfo?.twitter[0]?.avatar,
//                   },
//                 })
//               }
//               className="flex justify-center items-center gap-1 duration-200 p-1 rounded-lg hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
//             >
//               <span className="font-overline-medium">مشاهده گزارش ۳۶۰</span>
//               <CaretLeft size={12} />
//             </Link>
//           </div>
//         </Popup>
//       )}
//     </div>
//   );
// };

// export default memo(NetworkGraphChart);

// import { CaretLeft } from "@phosphor-icons/react";
// import Popup from "components/ui/PopUp";
// import { memo, useEffect, useRef, useState, useMemo, useCallback } from "react";
// import ForceGraph2D from "react-force-graph-2d";
// import fallbackImage from "/logo_small.png";
// import advanceSearch from "service/api/advanceSearch";
// import { buildRequestData } from "utils/requestData";
// import TextWrapper from "components/TextWrapper";
// import Divider from "components/ui/Divider";
// import SentimentAnalysis from "pages/user/hot-topic/components/ShowMoreDetail/SentimentAnalysis";
// import TopicCommunication from "pages/user/hot-topic/components/ShowMoreDetail/TopicCommunication";
// import ListDrawer from "pages/user/360-create/step-2-profile/components/ProfileList/ListDrawer";
// import { Link } from "react-router-dom";
// import { useReport360Store } from "store/report360Store";

// const NetworkGraphChart = ({ fetchedData, selectedNode, from, to }) => {
//   const [userInfo, setUserInfo] = useState(null);
//   const [isPopupOpen, setIsPopupOpen] = useState(false);
//   const [nodesCount, setNodesCount] = useState(0);
//   const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
//   const fgRef = useRef();
//   const containerRef = useRef();
//   const popupRef = useRef(); // Ref for popup
//   const setReport = useReport360Store((state) => state.setReport);

//   const handleNodeClick = useCallback(
//     async (node) => {
//       setIsPopupOpen(true);
//       try {
//         const filters = {
//           date: { from, to },
//           q: node?.node_name,
//         };
//         const infoQuery = buildRequestData(filters, "search_in_source");
//         const response = await advanceSearch.search(infoQuery);
//         setUserInfo(response?.data?.data);
//       } catch (error) {
//         console.error("Failed to fetch user info:", error);
//         setUserInfo(node);
//       }
//     },
//     [from, to]
//   );

//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const { width, height } = containerRef.current.getBoundingClientRect();
//         setDimensions({ width, height });
//       }
//     };
//     handleResize();
//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   useEffect(() => {
//     if (fetchedData?.nodes_count) {
//       setNodesCount(fetchedData.nodes_count);
//     }
//   }, [fetchedData]);

//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (popupRef.current && !popupRef.current.contains(event.target)) {
//         setIsPopupOpen(false);
//       }
//     };

//     if (isPopupOpen) {
//       document.addEventListener("mousedown", handleClickOutside);
//     } else {
//       document.removeEventListener("mousedown", handleClickOutside);
//     }

//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [isPopupOpen]);

//   const graphData = useMemo(
//     () => ({
//       nodes: fetchedData?.nodes || [],
//       links: fetchedData?.links || [],
//     }),
//     [fetchedData]
//   );

//   const getNodeColor = (node) => {
//     return selectedNode && node.node_name === selectedNode.node_name
//       ? "#4362B3"
//       : "#9E8DF3";
//   };

//   const connectedNodes = useMemo(() => {
//     if (!selectedNode || !graphData.links)
//       return { firstDegree: new Set(), secondDegree: new Set() };

//     const firstDegree = new Set();
//     const secondDegree = new Set();

//     graphData.links.forEach((link) => {
//       if (link.source.node_name === selectedNode.node_name) {
//         firstDegree.add(link.target.node_name);
//       } else if (link.target.node_name === selectedNode.node_name) {
//         firstDegree.add(link.source.node_name);
//       }
//     });

//     graphData.links.forEach((link) => {
//       if (
//         firstDegree.has(link.source.node_name) &&
//         link.target.node_name !== selectedNode.node_name &&
//         !firstDegree.has(link.target.node_name)
//       ) {
//         secondDegree.add(link.target.node_name);
//       } else if (
//         firstDegree.has(link.target.node_name) &&
//         link.source.node_name !== selectedNode.node_name &&
//         !firstDegree.has(link.source.node_name)
//       ) {
//         secondDegree.add(link.source.node_name);
//       }
//     });

//     return { firstDegree, secondDegree };
//   }, [selectedNode, graphData]);

//   return (
//     <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
//       <ForceGraph2D
//         ref={fgRef}
//         graphData={graphData}
//         linkColor={() => "#BEC4C5"}
//         nodeId="node_name"
//         dagLevelDistance={10}
//         nodeRelSize={10}
//         width={dimensions.width}
//         height={dimensions.height}
//         maxZoom={10}
//         minZoom={0.05}
//         nodeLabel="node_name"
//         onNodeClick={handleNodeClick}
//         nodeCanvasObject={(node, ctx, globalScale) => {
//           const label = "";
//           const isSelected =
//             selectedNode && node.node_name === selectedNode.node_name;
//           const isFirstDegree = connectedNodes.firstDegree.has(node.node_name);
//           const isSecondDegree = connectedNodes.secondDegree.has(
//             node.node_name
//           );
//           const radius = 5;

//           if (isFirstDegree) {
//             ctx.beginPath();
//             ctx.arc(node.x, node.y, radius + 6, 0, 2 * Math.PI, false);
//             ctx.fillStyle = "#4362B3";
//             ctx.fill();
//           }

//           ctx.beginPath();
//           ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI, false);
//           ctx.fillStyle = getNodeColor(node);
//           ctx.fill();

//           const fontSize = 12 / globalScale;
//           ctx.font = `${fontSize}px Sans-Serif`;
//           const textWidth = ctx.measureText(label).width;
//           const bckgDimensions = [textWidth, fontSize].map(
//             (n) => n + fontSize * 0.2
//           );

//           if (globalScale > 3) {
//             ctx.fillStyle = "#000000";
//             ctx.fillText(label, node.x + 8, node.y + 3);
//           }
//         }}
//       />
//       {userInfo && (
//         <Popup
//           hasButton={false}
//           isOpen={isPopupOpen}
//           onClose={() => setIsPopupOpen(false)}
//           hasCloseIcon={false}
//         >
//           <div ref={popupRef}>
//             <div className="flex flex-col gap-4">
//               <ListDrawer
//                 data={userInfo?.twitter[0]}
//                 media={"twitter"}
//                 showMediaName
//                 fallbackImg={fallbackImage}
//                 showHeaderMenu={false}
//                 className="!p-0"
//               >
//                 <TextWrapper media={"twitter"}>
//                   {userInfo?.content
//                     ? userInfo?.content
//                     : userInfo?.bio || userInfo?.twitter[0]?.bio}
//                 </TextWrapper>
//               </ListDrawer>
//               <Divider />
//               {userInfo.sentiment_negative &&
//                 userInfo.sentiment_positive &&
//                 userInfo.sentiment_neutral && (
//                   <>
//                     <SentimentAnalysis data={userInfo} />
//                     <Divider />
//                   </>
//                 )}
//               {userInfo.categories ? (
//                 <TopicCommunication data={userInfo} />
//               ) : null}
//             </div>
//             <div className="flex justify-center items-center gap-1 text-light-primary-text-rest mt-5">
//               <Link
//                 to={"/app/report-360/create"}
//                 onClick={() =>
//                   setReport({
//                     step: 3,
//                     type: "profile",
//                     profile: {
//                       id: userInfo?.twitter[0]?.id,
//                       user_title: userInfo?.twitter[0]?.user_title,
//                       user_name: userInfo?.twitter[0]?.user_name,
//                       platform: "twitter",
//                       avatar: userInfo?.twitter[0]?.avatar,
//                     },
//                   })
//                 }
//                 className="flex justify-center items-center gap-1 duration-200 p-1 rounded-lg hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
//               >
//                 <span className="font-overline-medium">مشاهده گزارش ۳۶۰</span>
//                 <CaretLeft size={12} />
//               </Link>
//             </div>
//           </div>
//         </Popup>
//       )}
//     </div>
//   );
// };

// export default memo(NetworkGraphChart);

import { CaretLeft } from "@phosphor-icons/react";
import Popup from "components/ui/PopUp";
import { memo, useEffect, useRef, useState, useMemo, useCallback } from "react";
import ForceGraph2D from "react-force-graph-2d";
import fallbackImage from "/logo_small.png";
import advanceSearch from "service/api/advanceSearch";
import { buildRequestData } from "utils/requestData";
import TextWrapper from "components/TextWrapper";
import Divider from "components/ui/Divider";
import SentimentAnalysis from "pages/user/hot-topic/components/ShowMoreDetail/SentimentAnalysis";
import TopicCommunication from "pages/user/hot-topic/components/ShowMoreDetail/TopicCommunication";
import ListDrawer from "./ListDrawer";
import { Link } from "react-router-dom";
import { useReport360Store } from "store/report360Store";

const NetworkGraphChart = ({ fetchedData, selectedNode, from, to }) => {
  const [userInfo, setUserInfo] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isPopupCooldown, setIsPopupCooldown] = useState(false); // New state for cooldown
  const [nodesCount, setNodesCount] = useState(0);
  const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
  const fgRef = useRef();
  const containerRef = useRef();
  const popupRef = useRef(); // Ref for popup
  const setReport = useReport360Store((state) => state.setReport);

  const handleNodeClick = useCallback(
    async (node) => {
      if (!isPopupCooldown) {
        setIsPopupOpen(true);
        try {
          const filters = {
            date: { from, to },
            q: node?.node_name,
          };
          const infoQuery = buildRequestData(filters, "search_in_source");
          const response = await advanceSearch.search(infoQuery);
          setUserInfo(response?.data?.data);
        } catch (error) {
          console.error("Failed to fetch user info:", error);
          setUserInfo(node);
        }
      }
    },
    [from, to, isPopupCooldown]
  );

  const handleClosePopup = useCallback(() => {
    setIsPopupOpen(false);
    setIsPopupCooldown(true);
    setTimeout(() => {
      setIsPopupCooldown(false);
    }, 1000);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        handleClosePopup();
      }
    };

    if (isPopupOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isPopupOpen, handleClosePopup]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (fetchedData?.nodes_count) {
      setNodesCount(fetchedData.nodes_count);
    }
  }, [fetchedData]);

  const graphData = useMemo(
    () => ({
      nodes: fetchedData?.nodes || [],
      links: fetchedData?.links || [],
    }),
    [fetchedData]
  );

  const getNodeColor = (node) => {
    return selectedNode && node.node_name === selectedNode.node_name
      ? "#4362B3"
      : "#9E8DF3";
  };

  const connectedNodes = useMemo(() => {
    if (!selectedNode || !graphData.links)
      return { firstDegree: new Set(), secondDegree: new Set() };

    const firstDegree = new Set();
    const secondDegree = new Set();

    graphData.links.forEach((link) => {
      if (link.source.node_name === selectedNode.node_name) {
        firstDegree.add(link.target.node_name);
      } else if (link.target.node_name === selectedNode.node_name) {
        firstDegree.add(link.source.node_name);
      }
    });

    graphData.links.forEach((link) => {
      if (
        firstDegree.has(link.source.node_name) &&
        link.target.node_name !== selectedNode.node_name &&
        !firstDegree.has(link.target.node_name)
      ) {
        secondDegree.add(link.target.node_name);
      } else if (
        firstDegree.has(link.target.node_name) &&
        link.source.node_name !== selectedNode.node_name &&
        !firstDegree.has(link.source.node_name)
      ) {
        secondDegree.add(link.source.node_name);
      }
    });

    return { firstDegree, secondDegree };
  }, [selectedNode, graphData]);

  return (
    <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
      <ForceGraph2D
        ref={fgRef}
        graphData={graphData}
        linkColor={() => "#BEC4C5"}
        nodeId="node_name"
        dagLevelDistance={10}
        nodeRelSize={10}
        width={dimensions.width}
        height={dimensions.height}
        maxZoom={10}
        minZoom={0.05}
        nodeLabel="node_name"
        onNodeClick={handleNodeClick}
        nodeCanvasObject={(node, ctx, globalScale) => {
          const label = "";
          const isSelected =
            selectedNode && node.node_name === selectedNode.node_name;
          const isFirstDegree = connectedNodes.firstDegree.has(node.node_name);
          const isSecondDegree = connectedNodes.secondDegree.has(
            node.node_name
          );
          const radius = 5;

          if (isFirstDegree) {
            ctx.beginPath();
            ctx.arc(node.x, node.y, radius + 6, 0, 2 * Math.PI, false);
            ctx.fillStyle = "#4362B3";
            ctx.fill();
          }

          ctx.beginPath();
          ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI, false);
          ctx.fillStyle = getNodeColor(node);
          ctx.fill();

          const fontSize = 12 / globalScale;
          ctx.font = `${fontSize}px Sans-Serif`;
          const textWidth = ctx.measureText(label).width;
          const bckgDimensions = [textWidth, fontSize].map(
            (n) => n + fontSize * 0.2
          );

          if (globalScale > 3) {
            ctx.fillStyle = "#000000";
            ctx.fillText(label, node.x + 8, node.y + 3);
          }
        }}
      />
      {userInfo && (
        <Popup
          hasButton={false}
          isOpen={isPopupOpen}
          onClose={handleClosePopup}
          hasCloseIcon={false}
        >
          <div ref={popupRef}>
            <div className="flex flex-col gap-4">
              <ListDrawer
                data={userInfo?.twitter[0]}
                media={"twitter"}
                showMediaName
                fallbackImg={fallbackImage}
                showHeaderMenu={false}
                className="!p-0"
              >
                <TextWrapper media={"twitter"}>
                  {userInfo?.content
                    ? userInfo?.content
                    : userInfo?.bio || userInfo?.twitter[0]?.bio}
                </TextWrapper>
              </ListDrawer>
              <Divider />
              {userInfo.sentiment_negative &&
                userInfo.sentiment_positive &&
                userInfo.sentiment_neutral && (
                  <>
                    <SentimentAnalysis data={userInfo} />
                    <Divider />
                  </>
                )}
              {userInfo.categories ? (
                <TopicCommunication data={userInfo} />
              ) : null}
            </div>
            <div className="flex justify-center items-center gap-1 text-light-primary-text-rest mt-5">
              <Link
                to={"/app/report-360/create"}
                onClick={() =>
                  setReport({
                    step: 3,
                    type: "profile",
                    profile: {
                      id: userInfo?.twitter[0]?.id,
                      user_title: userInfo?.twitter[0]?.user_title,
                      user_name: userInfo?.twitter[0]?.user_name,
                      platform: "twitter",
                      avatar: userInfo?.twitter[0]?.avatar,
                    },
                  })
                }
                className="flex justify-center items-center gap-1 duration-200 p-1 rounded-lg hover:text-[#6F5CD1] hover:bg-[#E9E6F7]"
              >
                <span className="font-overline-medium">مشاهده گزارش ۳۶۰</span>
                <CaretLeft size={12} />
              </Link>
            </div>
          </div>
        </Popup>
      )}
    </div>
  );
};

export default memo(NetworkGraphChart);
