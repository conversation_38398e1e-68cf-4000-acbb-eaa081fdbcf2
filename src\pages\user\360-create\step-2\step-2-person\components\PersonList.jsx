import { useState } from "react";
import PropTypes from "prop-types";
import {
  CaretLeft,
  InstagramLogo,
  Rss,
  TelegramLogo,
  XLogo,
} from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import Divider from "components/ui/Divider";
import { useReport360Store } from "store/report360Store";
import CardLoading from "components/ui/CardLoading";
import Paginate from "components/ui/Paginate";
import { toPersianNumber } from "utils/helper.js";
import use360requestStore from "store/360requestStore";

const PersonList = ({ nextStep, data, loading, total, page, setPage }) => {
  const [selectedItem, setSelectedItem] = useState(null);
  const setReport = useReport360Store((state) => state.setReport);
  const updateReport = use360requestStore((state) => state.updateReportField);

  const handleItemSelect = (item) => {
    updateReport("q", item);
    setReport({ entity: item });
    nextStep();
  };

  const personKeys = Object.keys(data || {});

  return (
    <>
      {loading ? (
        <div className="min-h-96 flex items-center text-center">
          <CardLoading />
        </div>
      ) : (
        <div className="flex flex-wrap pl-3">
          {personKeys.map((personKey, index) => {
            const personData = data[personKey];

            return (
              <div key={index} className="w-full md:w-1/3 px-3 mt-4">
                <Card
                  className="flex flex-col gap-1 !w-full m-1"
                  onClick={() => {
                    setSelectedItem(personKey);
                  }}
                >
                  <h3 className="font-headline-small">{personKey}</h3>
                  <span className="font-body-small text-[#7f7f7f]">
                    هویت فضای مجازی
                  </span>
                  <div className="flex items-center mt-3 !w-full">
                    <span className="font-body-medium w-1/5">مجموع محتوا:</span>
                    <div className="flex gap-8 pl-4 w-4/5">
                      {personData.twitter > 0 && (
                        <span className="font-body-small flex gap-2 items-center">
                          <XLogo size={18} color="#000000" />
                          {toPersianNumber(personData.twitter)}
                        </span>
                      )}
                      {personData.news > 0 && (
                        <span className="font-body-small flex gap-2 items-center">
                          <Rss size={18} color="#ECA213" />
                          {toPersianNumber(personData.news)}
                        </span>
                      )}
                      {personData.telegram > 0 && (
                        <span className="font-body-small flex gap-2 items-center">
                          <TelegramLogo size={18} color="#0084C7" />
                          {toPersianNumber(personData.telegram)}
                        </span>
                      )}
                      {personData.instagram > 0 && (
                        <span className="font-body-small flex gap-2 items-center">
                          <InstagramLogo size={18} color="#ECA213" />
                          {toPersianNumber(personData.instagram)}
                        </span>
                      )}
                    </div>
                  </div>
                  <Divider />
                  <div
                    className="flex justify-end items-center gap-1 text-light-primary-text-rest cursor-pointer mt-2"
                    onClick={() => handleItemSelect(personKey)}
                  >
                    <div className="flex justify-center items-center gap-3 duration-200 p-2 rounded-lg hover:text-[#6F5CD1] hover:bg-[#E9E6F7]">
                      <span className="font-button-small">مشاهده استعلام</span>
                      <CaretLeft size={12} />
                    </div>
                  </div>
                </Card>
              </div>
            );
          })}
        </div>
      )}
      <div className="flex justify-center">
        <Paginate
          page={page}
          setPage={setPage}
          dataCount={total}
          per_page={16}
        />
      </div>
    </>
  );
};

PersonList.propTypes = {
  nextStep: PropTypes.func.isRequired,
  data: PropTypes.object.isRequired,
  loading: PropTypes.bool,
  total: PropTypes.number.isRequired,
  page: PropTypes.number.isRequired,
  setPage: PropTypes.func.isRequired,
};

PersonList.propTypes = {
  nextStep: PropTypes.func.isRequired,
  data: PropTypes.object.isRequired,
  loading: PropTypes.bool,
  total: PropTypes.number.isRequired,
};

export default PersonList;
