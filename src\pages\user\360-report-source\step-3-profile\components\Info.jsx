import { memo } from "react";
import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";
import twitter from "../../../../../assets/images/360/twitter.png";
import telegram from "../../../../../assets/images/360/telegram.png";
import news from "../../../../../assets/images/360/news.png";
import {
  formatShortNumber,
  parseNumber,
  parseTimeToPersianSummary,
  toPersianNumber,
} from "utils/helper";
import "../../style.css";
import use360requestStore from "store/360requestStore";
import fallbackImg from "assets/images/default.png";
import ExportMenu from "components/ExportMenu/index.jsx";

const Info = () => {
  const sourceReport = use360requestStore((state) => ({
    profile: state.report?.content?.source_info,
    platform: state.report?.content?.report_platform,
  }));
  const platform = sourceReport.platform;
  const profile = sourceReport.profile;
  // const { isFromTopSource } = useReport360Store((state) => state.report);
  // const updateReportField = useReport360Store((state) => state.updateReport);
  // const updateReport = use360requestStore((state) => state.updateReportField);
  // const { chartData, setChartData } = useChartDataStore();

  // const profileCleared = useMemo(() => {
  //   return Array.isArray(profile.user_name)
  //     ? profile.user_name.filter(Boolean)
  //     : [profile.user_name || profile.key || profile.id].filter(Boolean);
  // }, [profile.user_name, profile.key, profile.id]);

  // const [profile, setProfile] = useState({
  //   user_name: prof?.user_name,
  //   platform: prof?.platform,
  //   following_count: null,
  //   follower_count: null,
  //   tweet_count: null,
  //   join_date: null,
  //   location: null,
  //   avatar: null,
  //   original_avatar: null,
  //   logo_image: null,
  //   ai_summary: null,
  //   founder: null,
  //   post_count: null,
  //   founded_year: null,
  //   office_location: null,
  //   gender: { label: null },
  //   postCount: null,
  //   photo_count: null,
  //   link_count: null,
  //   file_count: null,
  //   video_count: null,
  //   member_count: null,
  //   type: null,
  // });
  // const [loading, setLoading] = useState(false);

  // const cacheKey = `source_info_${platform || "unknown"}_${
  //   profile?.user_name || profile?.key || profile?.id
  // }_${date.start}_${date.end}`;

  // const updateProfileReport = useCallback(
  //   (data) => {
  //     updateReportField({
  //       profile: {
  //         ...data,
  //         original_avatar:
  //           data?.avatar || data?.original_avatar || data?.logo_image,
  //         avatar: data?.avatar || data?.logo_image,
  //         ai_summary: data?.ai_summary,
  //       },
  //     });
  //   },
  //   [updateReportField]
  // );

  const series = [
    {
      name: "Profile Metrics",
      data: [
        platform === "telegram"
          ? profile.member_count || 0
          : platform === "news"
          ? profile.post_count || 0
          : profile.follower_count || 0,
        platform === "telegram"
          ? (profile.photo_count || 0) +
            (profile.link_count || 0) +
            (profile.file_count || 0) +
            (profile.video_count || 0)
          : platform === "news"
          ? profile.post_count || 0
          : profile.tweet_count || 0,
        platform === "telegram"
          ? (profile.video_count || 0) + (profile.photo_count || 0)
          : 0,
      ].filter((val) => val !== null),
      time: [
        platform === "telegram"
          ? "تعداد اعضاء"
          : platform === "news"
          ? "تعداد محتوا"
          : "دنبال کننده",
        platform === "telegram"
          ? "پست"
          : platform === "news"
          ? "تعداد محتوا"
          : "توئیت",
        platform === "telegram" ? "محتوای تصویری" : null,
      ].filter((val) => val !== null),
    },
  ];

  const time = series[0].time;

  // useEffect(() => {
  //   if (isFromTopSource && profileCleared.length > 0 && platform) {
  //     const fetchData = async () => {
  //       setLoading(true);
  //       try {
  //         const results = await Promise.all(
  //           profileCleared.map(async (field) => {
  //             const profileCacheKey = `source_info_${platform}_${field}_${date.start}_${date.end}`;

  //             if (chartData[profileCacheKey]) {
  //               updateProfileReport(chartData[profileCacheKey]);
  //               return chartData[profileCacheKey];
  //             }

  //             const filters = { date, platform, q: field };
  //             const infoQuery = buildRequestData(filters, "source_info", 20);
  //             const response = await advanceSearch.search(infoQuery);
  //             const data = response.data.data?.[platform];

  //             setChartData(profileCacheKey, data);
  //             updateProfileReport(data);

  //             return data;
  //           })
  //         );
  //       } catch (error) {
  //         console.error("Error fetching profile data:", error);
  //       } finally {
  //         setLoading(false);
  //       }
  //     };

  //     fetchData();
  //   }
  // }, [isFromTopSource, date, platform, profileCleared]);

  // useEffect(() => {
  //   const fetchProfileData = async () => {
  //     if (chartData[cacheKey]) {
  //       setProfile((prev) => ({
  //         ...prev,
  //         ...chartData[cacheKey],
  //       }));
  //       return;
  //     }

  //     setLoading(true);
  //     try {
  //       const filters = {
  //         q: prof?.user_name,
  //         platform: prof?.platform,
  //         report_type: "search_in_source",
  //         date,
  //       };
  //       const infoQuery = buildRequestData(filters, "search_in_source");
  //       const response = await advanceSearch.search(infoQuery);

  //       // Check if response data exists and has the expected structure
  //       const platformData = response.data.data?.[platform];
  //       const data =
  //         platformData && Array.isArray(platformData) ? platformData[0] : null;

  //       if (data) {
  //         setChartData(cacheKey, data);

  //         setProfile((prev) => ({
  //           ...prev,
  //           following_count: data.following_count || prev.following_count,
  //           follower_count: data.follower_count || prev.follower_count,
  //           tweet_count: data.tweet_count || prev.tweet_count,
  //           join_date: data.join_date || prev.join_date,
  //           location: data.location || prev.location,
  //           avatar: data.avatar || data.logo_image || prev.avatar,
  //           original_avatar:
  //             data.avatar ||
  //             data.original_avatar ||
  //             data.logo_image ||
  //             prev.original_avatar,
  //           ai_summary: data.ai_summary || prev.ai_summary,
  //           founder: data.founder || prev.founder,
  //           post_count: data.post_count || prev.post_count,
  //           founded_year: data.founded_year || prev.founded_year,
  //           office_location: data.office_location || prev.office_location,
  //           gender: data.gender || prev.gender,
  //           postCount: data.postCount || prev.postCount,
  //           photo_count: data.photo_count || prev.photo_count,
  //           link_count: data.link_count || prev.link_count,
  //           file_count: data.file_count || prev.file_count,
  //           video_count: data.video_count || prev.video_count,
  //           member_count: data.member_count || prev.member_count,
  //           type: data.type || prev.type,
  //         }));
  //       }
  //     } catch (error) {
  //       console.error("Error fetching profile data:", error);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   fetchProfileData();
  // }, [prof?.user_name, prof?.platform, date]);

  // useEffect(() => {
  //   if (platform) {
  //     updateReport(`content.report_info.resource_overview.identity`, {
  //       following_count: profile.following_count,
  //       follower_count: profile.follower_count,
  //       tweet_count: profile.tweet_count,
  //       location: profile.location,
  //       join_date: profile.join_date,
  //       original_avatar:
  //         profile?.avatar || profile?.original_avatar || profile?.logo_image,
  //       avatar: profile?.avatar || profile?.logo_image,
  //       ai_summary: profile?.ai_summary,
  //     });
  //   }
  // }, [profile, platform]);

  const getPlatformName = () => {
    switch (platform) {
      case "instagram":
        return "اینستاگرام";
      case "twitter":
        return "توییتر";
      case "telegram":
        return "تلگرام";
      case "news":
        return "خبر";
      default:
        return "بستر";
    }
  };

  return (
    <Card className="flex flex-col gap-2 card-animation card-delay h-full">
      <div className="flex items-center justify-between">
        <Title title="شناسنامه" pos="left">
          {`این گزارش مشخصات کلی و آماری پروفایل مورد نظر را از ابتدای ایجاد آن در بستر (( ${getPlatformName()} )) نشان می‌دهد`}
        </Title>
        <ExportMenu
          chartSelector=".info-container"
          fileName="profile-info"
          series={series}
          time={time}
          excelHeaders={["Metric", "Value"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
          chartTitle="شناسنامه منبع"
        />
      </div>
      <div className="relative info-container">
        <div>
          <img
            src={profile?.avatar || profile?.logo_image}
            alt="user avatar"
            className="absolute top-[50%] left-[50%] transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full z-[10] mask-image-avatar"
            onError={(e) => {
              e.target.onError = null;
              e.target.src = fallbackImg;
            }}
          />
        </div>
        <h3 className="absolute top-[19%] left-[32%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
          {platform === "news"
            ? `صاحب امتیاز`
            : platform === "telegram"
            ? `نوع منبع`
            : "دنبال شونده"}
        </h3>
        <span className="absolute top-[28%] left-[32%] transform -translate-x-1/2 -translate-y-1/2 w-28 text-center text-light-neutral-text-medium z-[10] font-overline-medium">
          {platform === "news" && profile?.founder != null
            ? profile.founder
            : platform === "telegram"
            ? profile.type
            : profile.following_count
            ? toPersianNumber(parseNumber(profile.following_count))
            : "---"}
        </span>
        {platform !== "telegram" && platform !== "news" && (
          <>
            <h3 className="absolute top-[45%] left-[14%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
              دنبال کننده
            </h3>
            <span className="absolute top-[54%] left-[14%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
              {profile.follower_count
                ? toPersianNumber(parseNumber(profile.follower_count))
                : "---"}
            </span>
          </>
        )}
        <h3 className="absolute top-[71%] left-[32%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
          {platform === "news"
            ? `تعداد محتوا`
            : platform === "telegram"
            ? "تعداد اعضاء"
            : "توئیت"}
        </h3>
        <span className="absolute top-[80%] left-[32%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
          {platform === "news" && profile.post_count !== null
            ? toPersianNumber(parseNumber(profile?.post_count))
            : platform === "telegram"
            ? toPersianNumber(formatShortNumber(profile.member_count))
            : profile?.tweet_count
            ? toPersianNumber(parseNumber(profile?.tweet_count))
            : "---"}
        </span>
        <h3 className="absolute top-[19%] left-[68%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
          {platform === "news"
            ? `سال تاسیس`
            : platform === "telegram"
            ? "پست"
            : "تاریخ عضویت"}
        </h3>
        <span className="absolute top-[28%] left-[68%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
          {profile.founded_year != null
            ? toPersianNumber(profile.founded_year)
            : platform === "telegram"
            ? toPersianNumber(
                formatShortNumber(
                  profile?.photo_count +
                    profile?.link_count +
                    profile?.file_count +
                    profile?.video_count
                )
              )
            : profile.join_date != null
            ? parseTimeToPersianSummary(profile.join_date)
            : "---"}
        </span>
        {platform !== "telegram" && platform !== "news" && (
          <>
            <h3 className="absolute top-[45%] left-[86%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
              موقعیت
            </h3>
            <span className="absolute top-[54%] left-[86%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
              {profile.location || "---"}
            </span>
          </>
        )}
        <h3 className="absolute top-[71%] left-[68%] transform -translate-x-1/2 -translate-y-1/2 z-[10] font-subtitle-large">
          {platform === "news"
            ? `محل ستاد`
            : platform === "telegram"
            ? "محتوای تصویری"
            : "جنسیت"}
        </h3>
        <span className="absolute top-[80%] left-[68%] transform -translate-x-1/2 -translate-y-1/2 text-light-neutral-text-medium z-[10] font-overline-medium">
          {platform === "telegram"
            ? toPersianNumber(
                formatShortNumber(profile?.video_count + profile?.photo_count)
              )
            : platform === "news"
            ? profile?.office_location || "---"
            : profile?.gender?.label === "female"
            ? "زن"
            : profile?.gender?.label === "male"
            ? "مرد"
            : "---"}
        </span>
        <img
          src={
            platform === "telegram"
              ? telegram
              : platform === "news"
              ? news
              : twitter
          }
          className="w-full px-2"
          draggable="false"
          alt="diagram"
        />
      </div>
    </Card>
  );
};

export default memo(Info);
