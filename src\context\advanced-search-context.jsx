import { useState, createContext } from "react";
import P<PERSON><PERSON>ORMS from "constants/platforms.js";

export const SearchContext = createContext();
export const SearchProvider = ({ children }) => {
  const [filters, setFilters] = useState({
    platform: location?.state?.platform || PLATFORMS.TWITTER,
    date: {
      from: new Date(parseInt(Date.now()) - 86400000),
      to: new Date(),
    },
    sentiment: [],
    gender: [],
    subjectCategory: [],
    language: [],
    sources: [],
    keywords: [],
    hashtags: [],
    page: 1,
    sort: "date",
    sort_type: "نزولی",
  });
  const [query, setQuery] = useState("");
  const [isFilterListOpen, setIsFilterListOpen] = useState(false);

  const toggleOpenClose = () => setIsFilterListOpen(!isFilterListOpen);

  return (
    <SearchContext.Provider
      value={{
        filters,
        setFilters,
        query,
        setQuery,
        isFilterListOpen,
        setIsFilterListOpen,
        toggleOpenClose,
      }}
    >
      {children}
    </SearchContext.Provider>
  );
};
