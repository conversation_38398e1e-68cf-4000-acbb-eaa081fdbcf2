import { Card } from "components/ui/Card";
import Title from "pages/user/compare-create/components/step-two/charts/Title.jsx";

const PersonThoughtLine = () => {
  const tabs = [
    { id: "wordCloud", title: "خودی", color: "rgba(128, 128, 128, 1)" },
    { id: "cluster", title: "معاند", color: "rgba(255, 105, 180, 1)" },
    { id: "cluster", title: "خاکستری", color: "rgba(128, 128, 128, 1)" },
    { id: "cluster", title: "نامشخص", color: "rgba(128, 128, 128, 1)" },
  ];

  return (
    <Card className="h-full w-full">
      <div>
        <Title title="خط فکری"></Title>
        <div className="w-full mt-6 px-4 font-overline-medium flex flex-wrap gap-4">
          {tabs.map((tab, i) => (
            <span
              key={i}
              className="py-1 px-3 border rounded-md transition duration-300 hover:bg-[#EE9FAD]"
              style={{
                background: `linear-gradient(to left, ${tab.color} 0%, ${tab.color} 10%, rgba(255, 255, 255, 0) 100%)`,
              }}
            >
              {tab.title}
            </span>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default PersonThoughtLine;
