import { useState } from "react";
import { useReport360Store } from "store/report360Store";
import { Card } from "components/ui/Card";
import { CaretDown, User } from "@phosphor-icons/react";
import fallbackImg from "assets/images/default.png";
import PropTypes from "prop-types";

const ProfileSelector = () => {
  const { profiles, currentProfileId } = useReport360Store(
    (state) => state.report
  );
  const setCurrentProfile = useReport360Store(
    (state) => state.setCurrentProfile
  );
  const removeProfile = useReport360Store((state) => state.removeProfile);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const profileIds = Object.keys(profiles);
  const currentProfile = profiles[currentProfileId] || {};

  if (profileIds.length <= 1) {
    return null;
  }

  const handleProfileSelect = (profileId) => {
    setCurrentProfile(profileId);
    setIsDropdownOpen(false);
  };

  const handleRemoveProfile = (profileId, e) => {
    e.stopPropagation();
    removeProfile(profileId);
  };

  const getPlatformName = (platform) => {
    switch (platform) {
      case "twitter":
        return "توییتر";
      case "instagram":
        return "اینستاگرام";
      case "telegram":
        return "تلگرام";
      default:
        return "خبرگزاری";
    }
  };

  return (
    <Card className="mb-4 p-3">
      <div className="flex items-center w-full justify-start gap-4">
        <div className="flex items-center gap-2">
          <User size={20} />
          <span className="font-subtitle-medium">
            پروفایل‌های باز شده ({profileIds.length})
          </span>
        </div>

        <div className="relative">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-lg hover:bg-[#E9E6F7] transition-colors"
          >
            <img
              src={currentProfile.avatar || fallbackImg}
              alt="profile"
              className="w-6 h-6 rounded-full"
              onError={(e) => {
                e.target.onError = null;
                e.target.src = fallbackImg;
              }}
            />
            <span className="font-body-medium max-w-32 truncate">
              {currentProfile.user_title ||
                currentProfile.user_name ||
                "پروفایل"}
            </span>
            <CaretDown
              size={16}
              className={`transition-transform ${
                isDropdownOpen ? "rotate-180" : ""
              }`}
            />
          </button>

          {isDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
              <div className="max-h-60 overflow-y-auto">
                {profileIds.map((profileId) => {
                  const profile = profiles[profileId];
                  const isActive = profileId === currentProfileId;

                  return (
                    <div
                      key={profileId}
                      className={`flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 ${
                        isActive ? "bg-blue-50" : ""
                      }`}
                      onClick={() => handleProfileSelect(profileId)}
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <img
                          src={profile.avatar || fallbackImg}
                          alt="profile"
                          className="w-8 h-8 rounded-full flex-shrink-0"
                          onError={(e) => {
                            e.target.onError = null;
                            e.target.src = fallbackImg;
                          }}
                        />
                        <div className="min-w-0 flex-1">
                          <div className="font-subtitle-small truncate">
                            {profile.user_title ||
                              profile.user_name ||
                              "بدون نام"}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            @{profile.user_name} •{" "}
                            {getPlatformName(profile.platform)}
                          </div>
                        </div>
                      </div>

                      {profileIds.length > 1 && (
                        <button
                          onClick={(e) => handleRemoveProfile(profileId, e)}
                          className="text-red-500 hover:text-red-700 p-1 rounded"
                          title="حذف پروفایل"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </Card>
  );
};

ProfileSelector.propTypes = {
  isEdit: PropTypes.bool,
};

export default ProfileSelector;
